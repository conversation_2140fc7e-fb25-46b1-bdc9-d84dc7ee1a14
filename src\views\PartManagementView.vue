<template>
  <div class="part-management">
    <h1>零件管理</h1>
    <p>门店零件管理功能</p>
    
    <div class="management-sections">
      <div class="section">
        <h3>叫料清单管理</h3>
        <p>创建和管理叫料清单</p>
        <button class="btn" @click="showNewRequisition = true">新建叫料清单</button>
      </div>
      
      <div class="section">
        <h3>零件报废管理</h3>
        <p>处理零件报废申请</p>
        <button class="btn" @click="showScrapForm = true">新建报废申请</button>
      </div>
    </div>
    
    <!-- 新建叫料清单表单 -->
    <div v-if="showNewRequisition" class="modal">
      <div class="modal-content">
        <h3>新建叫料清单</h3>
        <NewRequisitionForm @close="showNewRequisition = false" />
      </div>
    </div>
    
    <!-- 报废表单 -->
    <div v-if="showScrapForm" class="modal">
      <div class="modal-content">
        <h3>新建报废申请</h3>
        <PartScrapForm @close="showScrapForm = false" />
      </div>
    </div>
  </div>
</template>

<script>
import NewRequisitionForm from '@/components/part-management/NewRequisitionForm.vue'
import PartScrapForm from '@/components/part-management/PartScrapForm.vue'

export default {
  name: 'PartManagementView',
  components: {
    NewRequisitionForm,
    PartScrapForm
  },
  data() {
    return {
      showNewRequisition: false,
      showScrapForm: false
    }
  }
}
</script>

<style scoped>
.part-management {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 1rem;
}

.management-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section h3 {
  color: #1890ff;
  margin-bottom: 1rem;
}

.btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn:hover {
  background: #40a9ff;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}
</style>
