### **【规则】流程图至蓝图文字生成规则**

**1. 任务目标**

此规则旨在将输入的流程图文件（如 `.drawio`）自动解析，并生成一份对应的、结构化的Markdown格式蓝图文字。

**2. 输入**

*   一个流程图文件（例如：`@潜客管理.drawio`）。

**3. 输出**

*   一个Markdown文件 (`.md`)。
*   **命名规范**：输出的MD文件名应为输入文件名后追加"蓝图"二字。
    *   例如：输入为 `潜客管理.drawio`，则输出为 `潜客管理蓝图.md`。
*   **存放位置**：生成的MD文件应与输入的流程图文件存放在同一目录下。

**4. 内容生成规范**

生成的Markdown文件必须包含一个表格，表格的结构和内容填充需严格遵循以下标准：

**表头结构:**

| 序号 | 动作 | 文件 | 描述 |
| :--- | :--- | :--- | :--- |

**各列填充规则:**

*   **`序号`列:**
    *   **来源:** 必须严格按照流程图中每个节点上标注的编号进行填充。
    *   **要求:** 编号必须与流程图完全一致，不可自行创造、修改或省略。例如，如果流程图标注为 `2a`，则此处必须填写 `2a`。

*   **`动作`列:**
    *   **来源:** 必须是流程图中对应节点的核心文字内容（除去序号部分）。
    *   **要求:** 内容必须与流程图完全一致，保持原意，不可自由发挥。

*   **`文件`列:**
    *   **来源:** 分析"动作"列的内容，推断出该动作直接产出、操作或引用的核心业务实体/文档。
    *   **示例:**
        *   动作"创建销售订单" -> 文件为"销售订单"。
        *   动作"更新潜客档案" -> 文件为"潜客档案"。
        *   动作"记录试驾信息" -> 文件为"试驾记录"。
    *   **要求:** 如果动作是纯粹的判断/决策（例如 "是否有意向购买？"）或不涉及具体文件实体，则此列用 `-` 填充。

*   **`描述`列:**
    *   **来源:** 对"动作"列进行简明扼要的陈述。
    *   **要求:**
        *   描述必须紧扣动作本身，只说明"做什么"，避免发散。
        *   语言应保持客观、精炼，通常为一句话。
        *   **错误示例:** "为了完成交易，需要创建订单，这对后续的财务和物流至关重要。"
        *   **正确示例:** "销售顾问为有购买意向的客户创建销售订单。"

---

**使用示例:**

当您需要我执行此任务时，可以这样指示：
"你好，请根据【流程图至蓝图文字生成规则】，帮我处理文件 `@车辆登记流程.drawio`。"

我将会遵循以上所有规定，为您生成 `车辆登记流程蓝图.md` 文件。 