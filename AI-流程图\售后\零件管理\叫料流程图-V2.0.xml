<mxfile host="app.diagrams.net" modified="2024-12-26T10:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" etag="YQzJ8vK9xK9xK9xK9xK9" version="22.1.11" type="device">
  <diagram name="叫料流程图-V2.0" id="material-request-flow-v20">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 泳道定义 -->
        <mxCell id="swimlane1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="350" height="1700" as="geometry" />
        </mxCell>
        
        <mxCell id="swimlane2" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="390" y="40" width="350" height="1700" as="geometry" />
        </mxCell>
        
        <mxCell id="swimlane3" value="ERP系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="40" width="350" height="1700" as="geometry" />
        </mxCell>
        
        <!-- 流程节点 -->
        <mxCell id="start1" value="门店操作员创建叫料清单" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="100" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="approval1" value="HQ审批员审批叫料清单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="500" y="200" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="decision1" value="审批结果?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="530" y="320" width="80" height="80" as="geometry" />
        </mxCell>

        <mxCell id="generate_po" value="系统接收数据生成采购订单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="850" y="450" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="check_inventory" value="HQ发货员检查库存情况" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="500" y="580" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="decision2" value="库存情况?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="530" y="700" width="80" height="80" as="geometry" />
        </mxCell>
        
        <!-- 全部发货路径 -->
        <mxCell id="full_ship" value="HQ发货员全部发货" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="500" y="840" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="full_receive" value="门店操作员全部收货" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="960" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="full_inventory" value="门店库存增加" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="1080" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="end1" value="流程结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="1200" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 部分发货路径 -->
        <mxCell id="partial_ship" value="HQ发货员部分发货" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="650" y="840" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="partial_receive" value="门店操作员部分收货" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="1320" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="partial_inventory" value="门店库存增加" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="1440" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="check_complete" value="系统检查采购订单状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="500" y="1560" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="decision3" value="是否全部发货完成?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="520" y="1680" width="100" height="80" as="geometry" />
        </mxCell>

        <mxCell id="end2" value="流程结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="650" y="1680" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 修改流程 -->
        <mxCell id="modify_request" value="门店操作员修改叫料清单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="320" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="resubmit" value="系统重新提交审批" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="150" y="440" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="start1" target="approval1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" edge="1" parent="1" source="approval1" target="decision1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge3" value="通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;fontColor=#000000;" edge="1" parent="1" source="decision1" target="generate_po">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge4" value="未通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;fontColor=#000000;" edge="1" parent="1" source="decision1" target="modify_request">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="generate_po" target="check_inventory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="check_inventory" target="decision2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" value="充足" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision2" target="full_ship">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" value="不足" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision2" target="partial_ship">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="full_ship" target="full_receive">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="full_receive" target="full_inventory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="full_inventory" target="end1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="partial_ship" target="partial_receive">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="partial_receive" target="partial_inventory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge14" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="partial_inventory" target="check_complete">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="check_complete" target="decision3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge16" value="完成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision3" target="end2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge17" value="未完成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision3" target="check_inventory">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="570" y="1800" />
              <mxPoint x="350" y="1800" />
              <mxPoint x="350" y="610" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="modify_request" target="resubmit">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge19" value="重新审批" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="resubmit" target="approval1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="220" y="520" />
              <mxPoint x="320" y="520" />
              <mxPoint x="320" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
