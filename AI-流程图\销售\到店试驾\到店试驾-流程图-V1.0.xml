<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="2">
  <diagram name="到店试驾流程图" id="UNIQUE-DIAGRAM-ID-V2">
    <mxGraphModel dx="1665" dy="756" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane-app" value="SUPER APP端（客户）" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="810" y="40" width="320" height="950" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-28" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;1b.客户注册Super APP&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="90" y="160" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-dealer" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="450" height="950" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-20" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;开始&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="125" y="40" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ppCPoxWIzudjN_2_lF5M-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="swimlane-dealer" source="node-create-record" target="node-record-feedback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="node-find-prospect" target="node-create-record" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="node-find-prospect" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;2.销售顾问通过手机号查找客户档案&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="90" y="360" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="node-check-app" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;1.客户是否注册Super APP？&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="65" y="150" width="190" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ogVAm-lZSBbBQpF5OQCp-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane-dealer" source="NlGhQrRZo6ftdErJHmv5-20" target="node-check-app" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="node-create-record" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;3.创建试驾登记单&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="100" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="node-record-feedback" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4.记录本次试驾情况&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="100" y="610" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="node-sync-to-factory" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;5.更新试驾记录&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="100" y="740" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ogVAm-lZSBbBQpF5OQCp-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="node-record-feedback" target="node-sync-to-factory" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-2" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6.查询并归档数据进行统计分析&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="550" y="740" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;结束&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="575" y="870" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-5" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;结束&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="125" y="870" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="node-sync-to-factory" target="vTFvDT6FCJa2eJiKhF__-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="vTFvDT6FCJa2eJiKhF__-2" target="vTFvDT6FCJa2eJiKhF__-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zKr2j84voKoQFW1vtLvN-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane-dealer" source="node-sync-to-factory" target="vTFvDT6FCJa2eJiKhF__-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-factory" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="40" width="320" height="950" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-24" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;1a.厂端潜客池存有潜客信息&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-factory" vertex="1">
          <mxGeometry x="90" y="270" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="node-check-app" target="NlGhQrRZo6ftdErJHmv5-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="340" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-40" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;是&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="NlGhQrRZo6ftdErJHmv5-38" vertex="1" connectable="0">
          <mxGeometry x="-0.8166" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="NlGhQrRZo6ftdErJHmv5-28" target="NlGhQrRZo6ftdErJHmv5-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="970" y="340" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="NlGhQrRZo6ftdErJHmv5-24" target="node-find-prospect" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="660" y="430" />
              <mxPoint x="270" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="node-check-app" target="NlGhQrRZo6ftdErJHmv5-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-41" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;否&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="NlGhQrRZo6ftdErJHmv5-36" vertex="1" connectable="0">
          <mxGeometry x="-0.1088" y="-2" relative="1" as="geometry">
            <mxPoint x="-165" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="In-Store Test Drive Flowchart" id="UNIQUE-DIAGRAM-ID-V2-EN">
    <mxGraphModel dx="1345" dy="608" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane-app-en" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="810" y="40" width="320" height="725" as="geometry" />
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="swimlane-app-en" source="NlGhQrRZo6ftdErJHmv5-28-en" target="7mGkjHAAhUBfOxKS13jK-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-28-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Registers SUPER APP&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="83" y="160" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="swimlane-app-en" source="7mGkjHAAhUBfOxKS13jK-4" target="NlGhQrRZo6ftdErJHmv5-28-en">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-4" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Start&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="swimlane-app-en">
          <mxGeometry x="118" y="56" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-7" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Make Test-drive appointment&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="swimlane-app-en">
          <mxGeometry x="83" y="390" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-dealer-en" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="450" height="725" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-20-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Start&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="125" y="40" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="node-check-app-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Has the prospect registered the SUPER APP?&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="65" y="150" width="190" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ogVAm-lZSBbBQpF5OQCp-1-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane-dealer-en" source="NlGhQrRZo6ftdErJHmv5-20-en" target="node-check-app-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="swimlane-dealer-en" source="node-create-record-en" target="node-record-feedback-en">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="node-create-record-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Create Test Drive Order&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="100" y="394" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="node-record-feedback-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Fill in Test Drive details&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="100" y="527" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-factory-en" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="40" width="320" height="725" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-24-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Update prospect info&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-factory-en" vertex="1">
          <mxGeometry x="90" y="270" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-2-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Query and analyze data&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-factory-en" vertex="1">
          <mxGeometry x="100" y="527" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-1-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;End&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-factory-en" vertex="1">
          <mxGeometry x="125" y="648" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="vTFvDT6FCJa2eJiKhF__-8-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-factory-en" source="vTFvDT6FCJa2eJiKhF__-2-en" target="vTFvDT6FCJa2eJiKhF__-1-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-38-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="node-check-app-en" target="NlGhQrRZo6ftdErJHmv5-24-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="340" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-40-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Yes&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="NlGhQrRZo6ftdErJHmv5-38-en" vertex="1" connectable="0">
          <mxGeometry x="-0.8166" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-36-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="node-check-app-en" target="NlGhQrRZo6ftdErJHmv5-28-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NlGhQrRZo6ftdErJHmv5-41-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;No&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="NlGhQrRZo6ftdErJHmv5-36-en" vertex="1" connectable="0">
          <mxGeometry x="-0.1088" y="-2" relative="1" as="geometry">
            <mxPoint x="-165" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="NlGhQrRZo6ftdErJHmv5-24-en" target="node-create-record-en">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="650" y="395" />
              <mxPoint x="200" y="395" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="NlGhQrRZo6ftdErJHmv5-28-en" target="NlGhQrRZo6ftdErJHmv5-24-en">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="963" y="340" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="7mGkjHAAhUBfOxKS13jK-7" target="node-create-record-en">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="738" y="464" />
              <mxPoint x="738" y="464" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="7mGkjHAAhUBfOxKS13jK-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="node-record-feedback-en" target="vTFvDT6FCJa2eJiKhF__-2-en">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
