<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="3">
  <diagram name="车辆保险投保" id="VehicleInsuranceFlow">
    <mxGraphModel dx="1665" dy="756" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="dms_store_lane" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="480" height="1530" as="geometry" />
        </mxCell>
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="205" y="60" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="query_orders" target="verify_buyer_info" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="query_orders" value="1.前置条件：订单已完成配车" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="verify_buyer_info" target="confirm_insurance" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="verify_buyer_info" value="2.销售顾问确认投保人信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="confirm_insurance" target="A8vJhuDTTOjCwDAFP7-2-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="confirm_insurance" value="3.销售顾问与客户线下确认保险方案" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="push_order" value="5.销售顾问推送订单信息至保险系统" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="A8vJhuDTTOjCwDAFP7-2-6" target="push_order" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-6" value="4.整车收退款管理&lt;br&gt;收取客户保险费用" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="180" y="520" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="update_status_processing" value="6.系统更新订单状态为投保中" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="350" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="push_order" target="update_status_processing" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="push_order" target="update_status_processing" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="260" y="900" as="sourcePoint" />
            <mxPoint x="680" y="1440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="contact_customer" target="x9uIazlUDyyDmHRxYoVH-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="contact_customer" value="9.销售顾问联系客户告知投保结果" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="165" y="1230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-25" target="x9uIazlUDyyDmHRxYoVH-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-38" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="x9uIazlUDyyDmHRxYoVH-37" vertex="1" connectable="0">
          <mxGeometry x="-0.2626" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-25" target="x9uIazlUDyyDmHRxYoVH-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-40" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="x9uIazlUDyyDmHRxYoVH-39" vertex="1" connectable="0">
          <mxGeometry x="-0.2694" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-25" value="10.是否需要收退款？" style="rhombus;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="160" y="1350" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-29" target="x9uIazlUDyyDmHRxYoVH-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="1485" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-29" value="10a.整车收退款管理&lt;br&gt;处理保险费用收退款" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="330" y="1350" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-31" value="投保流程结束" style="ellipse;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="187" y="1460" width="76" height="50" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-18" value="6b1.是否继续投保？" style="rhombus;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="70" y="870" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-18" target="contact_customer" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="20" y="910" />
              <mxPoint x="20" y="1260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-22" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="x9uIazlUDyyDmHRxYoVH-21" vertex="1" connectable="0">
          <mxGeometry x="-0.8687" relative="1" as="geometry">
            <mxPoint x="8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-18" target="confirm_insurance" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="145" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-4" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="0PrPLI3c57jo6W6Kbkxg-3" vertex="1" connectable="0">
          <mxGeometry x="-0.871" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="receive_policy" value="7.接收保单信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="330" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-14" value="8.订单管理&lt;div&gt;更新投保状态，显示保单详情&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="165" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-14" target="contact_customer" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="receive_policy" target="x9uIazlUDyyDmHRxYoVH-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="insurance_lane" value="保险系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="520" y="40" width="450" height="1530" as="geometry" />
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="insurance_lane" source="receive_order" target="process_insurance" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="receive_order" value="5.接收DMS推送的订单信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="process_insurance" value="5.执行投保处理流程" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="760" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="processing_check" value="6.投保处理成功?" style="rhombus;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="195" y="870" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="record_failure" value="6b.回传投保失败原因" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="30" y="880" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-11" value="6b1.销售顾问查看失败结果及原因" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="-185" y="880" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="insurance_lane" source="record_failure" target="x9uIazlUDyyDmHRxYoVH-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="generate_policy" value="6a.生成保单信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="1000" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="return_policy" value="6a1.回传保单信息至DMS" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="start" target="query_orders" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="update_status_processing" target="receive_order" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="process_insurance" target="processing_check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="processing_check" target="record_failure" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-9" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge25" vertex="1" connectable="0">
          <mxGeometry x="-0.6483" y="1" relative="1" as="geometry">
            <mxPoint x="-21" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="processing_check" target="generate_policy" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-10" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge26" vertex="1" connectable="0">
          <mxGeometry x="-0.4148" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="generate_policy" target="return_policy" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="x9uIazlUDyyDmHRxYoVH-11" target="x9uIazlUDyyDmHRxYoVH-18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="return_policy" target="receive_policy" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="车辆保险投保_en" id="VehicleInsuranceFlow_en">
    <mxGraphModel dx="1281" dy="582" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="dms_store_lane" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="480" height="1530" as="geometry" />
        </mxCell>
        <mxCell id="start" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="205" y="60" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="query_orders" target="verify_buyer_info" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="query_orders" value="1. Prerequisite: Vehicle allocated for the order" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="verify_buyer_info" target="confirm_insurance" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="verify_buyer_info" value="2. SA confirms policyholder information" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="confirm_insurance" target="A8vJhuDTTOjCwDAFP7-2-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="confirm_insurance" value="3. SA confirms insurance plan with customer offline" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="push_order" value="5. SA pushes order info to Insurance Mgt. System" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="190" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="A8vJhuDTTOjCwDAFP7-2-6" target="push_order" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-6" value="4. Vehicle Payment &amp;amp; Refund Mgt.&lt;br&gt;Collect insurance fee from customer" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="180" y="520" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="update_status_processing" value="6. System updates order status to &#39;Insurance in Progress&#39;" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="350" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="push_order" target="update_status_processing" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="A8vJhuDTTOjCwDAFP7-2-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="push_order" target="update_status_processing" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="260" y="900" as="sourcePoint" />
            <mxPoint x="680" y="1440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="contact_customer" target="x9uIazlUDyyDmHRxYoVH-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="contact_customer" value="9. SA contacts customer to inform of insurance result" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="165" y="1230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-25" target="x9uIazlUDyyDmHRxYoVH-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-38" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="x9uIazlUDyyDmHRxYoVH-37" vertex="1" connectable="0">
          <mxGeometry x="-0.2626" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-25" target="x9uIazlUDyyDmHRxYoVH-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-40" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="x9uIazlUDyyDmHRxYoVH-39" vertex="1" connectable="0">
          <mxGeometry x="-0.2694" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-25" value="10. Is payment/refund needed?" style="rhombus;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="160" y="1350" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-29" target="x9uIazlUDyyDmHRxYoVH-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="1485" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-29" value="10a. Vehicle Payment &amp;amp; Refund Mgt.&lt;br&gt;Process insurance fee payment/refund" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="330" y="1350" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-31" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="187" y="1460" width="76" height="50" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-18" value="6b1. Continue with insurance application?" style="rhombus;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="70" y="870" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-18" target="contact_customer" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="20" y="910" />
              <mxPoint x="20" y="1260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-22" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="x9uIazlUDyyDmHRxYoVH-21" vertex="1" connectable="0">
          <mxGeometry x="-0.8687" relative="1" as="geometry">
            <mxPoint x="8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-18" target="confirm_insurance" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="145" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-4" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="0PrPLI3c57jo6W6Kbkxg-3" vertex="1" connectable="0">
          <mxGeometry x="-0.871" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="receive_policy" value="7. Receives policy info" style="rounded=1;whiteSpace=wrap;html=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="330" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-14" value="8. Order Management&lt;div&gt;Update insurance status, display policy basic info&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="dms_store_lane" vertex="1">
          <mxGeometry x="147.5" y="1110" width="155" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="dms_store_lane" source="x9uIazlUDyyDmHRxYoVH-14" target="contact_customer" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="dms_store_lane" source="receive_policy" target="x9uIazlUDyyDmHRxYoVH-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="insurance_lane" value="Insurance Mgt. System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="520" y="40" width="450" height="1530" as="geometry" />
        </mxCell>
        <mxCell id="0PrPLI3c57jo6W6Kbkxg-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="insurance_lane" source="receive_order" target="process_insurance" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="receive_order" value="5. Receives order info pushed from DMS" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="640" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="process_insurance" value="5. Executes insurance application process" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="760" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="processing_check" value="6. Insurance application successful?" style="rhombus;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="195" y="870" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="record_failure" value="6b. Sends back reason for insurance failure" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="30" y="880" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-11" value="6b1. SA checks failure result and reason" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="-185" y="880" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="insurance_lane" source="record_failure" target="x9uIazlUDyyDmHRxYoVH-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="generate_policy" value="6a. Generates policy info" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="1000" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="return_policy" value="6a1. Sends back policy info to DMS" style="rounded=1;whiteSpace=wrap;html=1;" parent="insurance_lane" vertex="1">
          <mxGeometry x="210" y="1110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="start" target="query_orders" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="update_status_processing" target="receive_order" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="process_insurance" target="processing_check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="processing_check" target="record_failure" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-9" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge25" vertex="1" connectable="0">
          <mxGeometry x="-0.6483" y="1" relative="1" as="geometry">
            <mxPoint x="-21" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="processing_check" target="generate_policy" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-10" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge26" vertex="1" connectable="0">
          <mxGeometry x="-0.4148" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="generate_policy" target="return_policy" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="x9uIazlUDyyDmHRxYoVH-11" target="x9uIazlUDyyDmHRxYoVH-18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="x9uIazlUDyyDmHRxYoVH-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="return_policy" target="receive_policy" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="5as34409C1Gyl6_iCnMn" name="第 3 页">
    <mxGraphModel dx="1170" dy="529" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="FBnhsNl6P_DGVdiA23SG-1" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="60" y="40" width="480" height="922" as="geometry" />
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="FBnhsNl6P_DGVdiA23SG-1" source="FBnhsNl6P_DGVdiA23SG-2" target="FBnhsNl6P_DGVdiA23SG-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-2" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" vertex="1">
          <mxGeometry x="205" y="60" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="G32wtEJKZke_IwK53VH2-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="FBnhsNl6P_DGVdiA23SG-1" source="FBnhsNl6P_DGVdiA23SG-8" target="FBnhsNl6P_DGVdiA23SG-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-8" value="1.Confirm insurance plan with customer offline" style="rounded=1;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" vertex="1">
          <mxGeometry x="190" y="150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-9" value="3. Push order info" style="rounded=1;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" vertex="1">
          <mxGeometry x="190" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="FBnhsNl6P_DGVdiA23SG-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="290" y="1380" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-18" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="FBnhsNl6P_DGVdiA23SG-17" vertex="1" connectable="0">
          <mxGeometry x="-0.2626" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="225" y="1410" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-20" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="FBnhsNl6P_DGVdiA23SG-19" vertex="1" connectable="0">
          <mxGeometry x="-0.2694" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="FBnhsNl6P_DGVdiA23SG-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="1485" />
            </Array>
            <mxPoint x="400" y="1410" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G32wtEJKZke_IwK53VH2-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="FBnhsNl6P_DGVdiA23SG-1" source="FBnhsNl6P_DGVdiA23SG-25" target="yHJLhY4tcvNkid9fHq38-10">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="34" y="570" />
              <mxPoint x="34" y="857" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="G32wtEJKZke_IwK53VH2-2" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="G32wtEJKZke_IwK53VH2-1">
          <mxGeometry x="-0.4791" relative="1" as="geometry">
            <mxPoint y="-35" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-25" value="4b.Insurance application again?" style="rhombus;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" vertex="1">
          <mxGeometry x="70" y="530" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="FBnhsNl6P_DGVdiA23SG-1" source="FBnhsNl6P_DGVdiA23SG-25" target="FBnhsNl6P_DGVdiA23SG-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="145" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-29" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="FBnhsNl6P_DGVdiA23SG-28" vertex="1" connectable="0">
          <mxGeometry x="-0.871" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-10" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" vertex="1">
          <mxGeometry x="107" y="832" width="76" height="50" as="geometry" />
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="FBnhsNl6P_DGVdiA23SG-1" source="yHJLhY4tcvNkid9fHq38-22" target="yHJLhY4tcvNkid9fHq38-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="145" y="830" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-22" value="&amp;nbsp;5.&lt;span style=&quot;color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); background-color: transparent;&quot;&gt;Update insurance info&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-1" vertex="1">
          <mxGeometry x="85" y="690" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-34" value="VIPRO System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="540" y="40" width="450" height="923" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-38" value="4. Insurance application successful?" style="rhombus;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-34" vertex="1">
          <mxGeometry x="170" y="530" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-42" value="4a. Generates policy info" style="rounded=1;whiteSpace=wrap;html=1;" parent="FBnhsNl6P_DGVdiA23SG-34" vertex="1">
          <mxGeometry x="185" y="690" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="FBnhsNl6P_DGVdiA23SG-38" target="FBnhsNl6P_DGVdiA23SG-42" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FBnhsNl6P_DGVdiA23SG-50" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="FBnhsNl6P_DGVdiA23SG-49" vertex="1" connectable="0">
          <mxGeometry x="-0.4148" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="FBnhsNl6P_DGVdiA23SG-9" target="FBnhsNl6P_DGVdiA23SG-38" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="FBnhsNl6P_DGVdiA23SG-38" target="FBnhsNl6P_DGVdiA23SG-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-6" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="yHJLhY4tcvNkid9fHq38-5" vertex="1" connectable="0">
          <mxGeometry x="-0.7011" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yHJLhY4tcvNkid9fHq38-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="FBnhsNl6P_DGVdiA23SG-42" target="yHJLhY4tcvNkid9fHq38-22" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="282.5" y="760" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
