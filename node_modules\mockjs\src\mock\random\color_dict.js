/*
    ## Color 字典数据

    字典数据来源 [A nicer color palette for the web](http://clrs.cc/)
*/
module.exports = {
    // name value nicer
    navy: {
        value: '#000080',
        nicer: '#001F3F'
    },
    blue: {
        value: '#0000ff',
        nicer: '#0074D9'
    },
    aqua: {
        value: '#00ffff',
        nicer: '#7FDBFF'
    },
    teal: {
        value: '#008080',
        nicer: '#39CCCC'
    },
    olive: {
        value: '#008000',
        nicer: '#3D9970'
    },
    green: {
        value: '#008000',
        nicer: '#2ECC40'
    },
    lime: {
        value: '#00ff00',
        nicer: '#01FF70'
    },
    yellow: {
        value: '#ffff00',
        nicer: '#FFDC00'
    },
    orange: {
        value: '#ffa500',
        nicer: '#FF851B'
    },
    red: {
        value: '#ff0000',
        nicer: '#FF4136'
    },
    maroon: {
        value: '#800000',
        nicer: '#85144B'
    },
    fuchsia: {
        value: '#ff00ff',
        nicer: '#F012BE'
    },
    purple: {
        value: '#800080',
        nicer: '#B10DC9'
    },
    silver: {
        value: '#c0c0c0',
        nicer: '#DDDDDD'
    },
    gray: {
        value: '#808080',
        nicer: '#AAAAAA'
    },
    black: {
        value: '#000000',
        nicer: '#111111'
    },
    white: {
        value: '#FFFFFF',
        nicer: '#FFFFFF'
    }
}