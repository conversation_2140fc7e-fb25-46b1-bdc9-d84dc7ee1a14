# 拣货管理功能蓝图

## 功能概述
本蓝图描述了DMS系统中拣货管理的完整业务流程，涵盖从生成拣货单、库存判断、拣货操作、缺货处理、叫料补货到退料管理的全过程。

## 业务流程功能点

| 序号 | 参与角色 | 动作 | 涉及文件（输入/输出） | 描述 |
|------|----------|------|----------------------|------|
|  | 系统管理员 | 生成拣货单 | 无文件交互 | 客户确认完工单后会自动生成拣货单，包含所需零件的详细信息和数量 |
| 1 | 系统管理员 | 判断门店零件库存 | 无文件交互 | 系统自动检查门店当前库存状态，判断拣货单中各零件的库存可用性 |
| 2 | 门店库存管理员 | 查看库存是否充足 | 无文件交互 | 根据系统库存判断结果，确认拣货单中的零件库存是否充足 |
| 3a | 门店拣货员 | 开始拣货 | 无文件交互 | 当库存充足时，拣货员根据拣货单开始从库房中拣取所需零件 |
| 4 | 门店拣货员 | 拣货完成 | 无文件交互 | 拣货员完成所有零件的拣取工作，在系统中点击“拣货完成” |
| 5 | 系统管理员 | 自动扣减门店库存 | 无文件交互 | 拣货完成后，系统自动从门店库存中扣减已拣取的零件数量 |
| 6 | 门店库存管理员 | 判断是否需要退料 | 无文件交互 | 在操作完修车后，技师确认完工前，可对多余或不需要的零件需要退回仓库 |
| 7 | 门店库存管理员 | 库存增加（退料） | 无文件交互 | 当需要退料时，将多余零件退回库存，系统自动增加相应的库存数量 |
| 3b | 门店用户 | 显示缺货状态 | 无文件交互 | 当库存不足时，拣货单显示为缺货状态，并等待门店补货 |
| 4b | 门店用户 | 创建叫料单 | 无文件交互 | 针对缺货零件创建叫料申请单，申请从厂端补充库存 |
| 5b | 厂端审批人员 | 审批叫料单 | 无文件交互 | 厂端接收并审批门店提交的叫料申请单 |
| 6b | ERP系统管理员 | 生成采购订单 | 无文件交互 | 叫料单审批通过后，ERP系统自动生成相应的采购订单 |
| 7b | 厂端发货人员 | 发货 | 无文件交互 | 厂端根据采购订单安排发货，将所需零件发送给门店 |
| 8b | 门店收货人员 | 收货 | 无文件交互 | 门店接收厂端发出的零件，核对货物数量完成收货操作 |
| 9b | 门店库存管理员 | 锁定库存 | 无文件交互 | 门店完成收货后，根据缺货状态的拣货单中所需零件种类和数量优先对相应的库存进行锁定 |
|  | 系统管理员 | 流程结束 | 无文件交互 | 当技师确认完工后，拣货流程关闭 |

## 业务规则说明

### 库存判断规则
- 系统自动判断门店库存是否满足拣货单需求
- 库存充足时直接进入拣货流程
- 库存不足时进入缺货处理流程

### 拣货操作规则
- 拣货员必须按照拣货单进行拣货操作
- 拣货完成后系统自动扣减库存
- 支持拣货过程中的退料操作

### 缺货处理规则
- 缺货时自动显示缺货状态
- 需要创建叫料单申请补货
- 叫料单需要厂端审批通过

### 补货流程规则
- 审批通过的叫料单自动生成采购订单
- 厂端发货后门店需要及时收货
- 收货后需要锁定库存确保用于当前拣货需求

### 退料管理规则
- 支持拣货过程中的退料操作
- 退料时自动增加门店库存
- 退料完成后流程正常结束

## 角色职责

### 门店角色
- **门店拣货员**：负责执行拣货操作
- **门店库存管理员**：负责库存状态管理、退料判断和库存锁定
- **门店收货人员**：负责接收厂端发货
- **门店用户**：负责查看缺货状态和创建叫料单

### 厂端角色  
- **厂端审批人员**：负责审批叫料单申请
- **厂端发货人员**：负责安排发货

### 系统角色
- **系统管理员**：负责拣货单生成、库存判断、自动扣减和流程监控
- **ERP系统管理员**：负责采购订单生成

## 流程特点

### 智能化程度高
- 系统自动生成拣货单和判断库存
- 自动扣减库存和流程状态更新
- 支持缺货自动处理

### 双路径设计
- 库存充足：直接拣货路径
- 库存不足：缺货处理路径
- 两个路径最终都能完成拣货需求

### 完整的补货机制
- 缺货时自动触发叫料流程
- 包含审批、采购、发货、收货完整环节
- 收货后锁定库存确保拣货需求

### 灵活的退料支持
- 支持拣货过程中的退料操作
- 自动处理退料库存增加
- 确保库存数据的准确性
