<mxfile host="app.diagrams.net" modified="2024-12-26T10:05:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" etag="YQzJ8vK9xK9xK9xK9xK9" version="22.1.11" type="device">
  <diagram name="零件流程汇总流程图-V1.1优化版" id="parts-process-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="2500" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 泳道定义 -->
        <mxCell id="swimlane1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="350" height="2200" as="geometry" />
        </mxCell>

        <mxCell id="swimlane2" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="390" y="40" width="350" height="2200" as="geometry" />
        </mxCell>

        <mxCell id="swimlane3" value="ERP系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="40" width="350" height="2200" as="geometry" />
        </mxCell>
        
        <!-- 流程节点 -->
        <mxCell id="start1" value="门店操作员新建叫料清单" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="100" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="approval1" value="HQ审批员审批叫料单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="200" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="decision1" value="审批结果?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="530" y="320" width="80" height="80" as="geometry" />
        </mxCell>

        <mxCell id="generate_po" value="系统自动生成采购订单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="450" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="wait_goods" value="门店操作员等待收货" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="580" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="receive_goods" value="门店操作员执行收货" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="700" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="update_erp" value="系统接收收货数据" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="820" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="update_inventory" value="DMS门店库存增加" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="940" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="damage_form" value="门店操作员填写报损单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1060" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="submit_damage" value="门店操作员提交报损单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1180" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="approval2" value="HQ审批员审批报损单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="1300" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="decision2" value="报损审批结果?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="530" y="1420" width="80" height="80" as="geometry" />
        </mxCell>

        <mxCell id="damage_to_erp" value="系统接收报损信息" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="1540" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="generate_pick" value="系统生成拣货单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1660" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="check_inventory" value="库存是否满足?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="1780" width="80" height="80" as="geometry" />
        </mxCell>

        <mxCell id="picking" value="门店操作员执行拣货" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1920" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="shortage" value="拣货单进入缺货状态" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="1780" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="wait_stock" value="等待库存补充" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="1900" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="return_decision" value="是否需要退料?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="2040" width="80" height="80" as="geometry" />
        </mxCell>

        <mxCell id="return_parts" value="门店操作员执行退料" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="2160" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="increase_inventory" value="零件库存增加" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="2280" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="end1" value="结束" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="2400" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="end2" value="结束" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="270" y="2160" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 修改叫料单流程 -->
        <mxCell id="modify_order" value="门店操作员修改叫料单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="320" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="regenerate_order" value="系统重新生成叫料单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="440" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 修改报损单流程 -->
        <mxCell id="modify_damage" value="门店操作员修改报损单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1420" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="regenerate_damage" value="系统重新生成报损单" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1540" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start1" target="approval1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="approval1" target="decision1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" value="通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision1" target="generate_po">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge4" value="未通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision1" target="modify_order">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="generate_po" target="wait_goods">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="wait_goods" target="receive_goods">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="receive_goods" target="update_erp">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="update_erp" target="update_inventory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="update_inventory" target="damage_form">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="damage_form" target="submit_damage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="submit_damage" target="approval2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="approval2" target="decision2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" value="通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision2" target="damage_to_erp">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge14" value="未通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision2" target="modify_damage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="damage_to_erp" target="generate_pick">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 循环连接线 -->
        <mxCell id="edge16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="modify_order" target="regenerate_order">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge17" value="重新审批" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="regenerate_order" target="approval1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="220" y="520" />
              <mxPoint x="350" y="520" />
              <mxPoint x="350" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="modify_damage" target="regenerate_damage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge19" value="重新审批" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="regenerate_damage" target="approval2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="220" y="1620" />
              <mxPoint x="350" y="1620" />
              <mxPoint x="350" y="1330" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 拣货流程连接线 -->
        <mxCell id="edge20" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="generate_pick" target="check_inventory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge21" value="满足" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="check_inventory" target="picking">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge22" value="不满足" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="check_inventory" target="shortage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge23" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="shortage" target="wait_stock">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge24" value="库存补充后" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="wait_stock" target="check_inventory">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="570" y="1980" />
              <mxPoint x="680" y="1980" />
              <mxPoint x="680" y="1820" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="picking" target="return_decision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge26" value="需要" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="return_decision" target="return_parts">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge27" value="不需要" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="return_decision" target="end2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge28" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="return_parts" target="increase_inventory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="increase_inventory" target="end1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
