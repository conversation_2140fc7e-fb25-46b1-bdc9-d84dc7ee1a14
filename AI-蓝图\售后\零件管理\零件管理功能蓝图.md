# 零件管理功能蓝图

## 功能概述
零件管理模块主要负责售后服务过程中的零件需求管理、损耗管理和库存拣货管理，包含新建叫料清单、零件报损、查看报损记录、查看拣货单四个核心功能。

## 功能详细列表

| 序号 | 参与角色 | 动作 | 涉及文件（输入/输出） | 描述 |
|------|----------|------|---------------------|------|
| 1 | 技师 | 查看零件库存信息 | 无文件交互 | 在新建叫料清单前，查看当前零件的库存状态和可用数量 |
| 2 | 技师 | 搜索零件信息 | 无文件交互 | 通过零件名称、零件号、适配车型等条件搜索需要的零件 |
| 3 | 技师 | 新建叫料清单 | 无文件交互 | 根据维修工单需求，创建新的叫料清单，填写零件信息和数量 |
| 4 | 技师 | 填写叫料清单详情 | 无文件交互 | 在叫料清单中添加具体的零件名称、零件号、需求数量、紧急程度等信息 |
| 5 | 技师 | 提交叫料清单 | 无文件交互 | 完成叫料清单填写后提交给仓库管理员处理 |
| 6 | 仓库管理员 | 查看叫料清单列表 | 无文件交互 | 查看所有待处理的叫料清单，了解零件需求情况 |
| 7 | 仓库管理员 | 审核叫料清单 | 无文件交互 | 对技师提交的叫料清单进行审核，确认零件需求的合理性 |
| 8 | 仓库管理员 | 生成拣货单 | 无文件交互 | 根据审核通过的叫料清单生成拣货单，系统自动创建拣货任务 |
| 9 | 仓库管理员 | 查看拣货单列表 | 无文件交互 | 查看所有拣货单的状态，包括待拣货、拣货中、已完成等状态 |
| 10 | 仓库管理员 | 查看拣货单详情 | 无文件交互 | 查看具体拣货单的详细信息，包括零件清单、拣货进度等 |
| 11 | 仓库管理员 | 执行拣货操作 | 无文件交互 | 按照拣货单进行实际的零件拣选，更新拣货状态 |
| 12 | 仓库管理员 | 完成拣货单 | 无文件交互 | 完成所有零件拣选后，将拣货单状态更新为已完成 |
| 13 | 技师 | 记录零件报损 | 无文件交互 | 当零件在使用过程中出现损坏、过期等情况时，记录报损信息 |
| 14 | 技师 | 填写报损原因 | 无文件交互 | 详细填写零件报损的具体原因，如损坏、过期、质量问题等 |
| 15 | 技师 | 上传报损凭证 | 无文件交互 | 上传零件报损的相关照片或文档作为凭证 |
| 16 | 技师 | 提交报损申请 | 无文件交互 | 完成报损信息填写后提交给上级审核 |
| 17 | 仓库管理员 | 查看报损申请列表 | 无文件交互 | 查看所有待审核的零件报损申请 |
| 18 | 仓库管理员 | 审核报损申请 | 无文件交互 | 对报损申请进行审核，确认报损的真实性和合理性 |
| 19 | 仓库管理员 | 批准报损申请 | 无文件交互 | 审核通过后批准报损申请，系统自动更新库存数量 |
| 20 | 仓库管理员 | 驳回报损申请 | 无文件交互 | 对不合理的报损申请进行驳回，并说明驳回原因 |
| 21 | 店长 | 查看报损记录列表 | 无文件交互 | 查看所有已处理的零件报损记录，监控报损情况 |
| 22 | 店长 | 筛选报损记录 | 无文件交互 | 按照时间范围、零件类型、报损原因等条件筛选报损记录 |
| 23 | 店长 | 查看报损记录详情 | 无文件交互 | 查看具体报损记录的详细信息，包括报损原因、处理结果等 |
| 24 | 店长 | 分析报损趋势 | 无文件交互 | 通过报损记录分析零件损耗趋势，为库存管理提供决策支持 |
| 25 | 仓库管理员 | 导出叫料清单 | **输出:** Excel文件 | 将叫料清单数据导出为Excel文件，便于统计和分析 |
| 26 | 仓库管理员 | 导出拣货单 | **输出:** Excel文件 | 将拣货单数据导出为Excel文件，便于打印和存档 |
| 27 | 店长 | 导出报损记录 | **输出:** Excel文件 | 将报损记录导出为Excel文件，用于报表统计和上级汇报 |
| 28 | 仓库管理员 | 打印拣货单 | **输出:** PDF文件 | 打印拣货单用于现场拣货操作 |

## 业务流程说明

### 叫料清单流程
1. 技师根据维修需求查看零件库存
2. 技师搜索并选择需要的零件
3. 技师新建叫料清单并填写详情
4. 技师提交叫料清单
5. 仓库管理员审核叫料清单
6. 审核通过后系统生成拣货单

### 拣货单处理流程
1. 仓库管理员查看拣货单列表
2. 仓库管理员查看具体拣货单详情
3. 仓库管理员执行拣货操作
4. 完成拣货后更新拣货单状态

### 零件报损流程
1. 技师发现零件损坏或异常
2. 技师记录报损信息并填写原因
3. 技师上传报损凭证
4. 技师提交报损申请
5. 仓库管理员审核报损申请
6. 审核通过后系统自动更新库存

### 报损记录管理
1. 店长定期查看报损记录
2. 店长筛选和分析报损数据
3. 店长基于报损趋势制定库存策略

## 角色权限说明

### 技师
- 可以查看零件库存信息
- 可以新建和提交叫料清单
- 可以记录和提交零件报损申请

### 仓库管理员
- 可以审核叫料清单和报损申请
- 可以生成和管理拣货单
- 可以执行拣货操作
- 可以导出相关数据和打印单据

### 店长
- 可以查看所有报损记录
- 可以分析报损趋势
- 可以导出报损统计数据
