<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="3">
  <diagram name="潜客跟进流程图" id="PROSPECT-FOLLOW-UP-OPTIMIZED">
    <mxGraphModel dx="1345" dy="608" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane-factory" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="320" height="1560" as="geometry" />
        </mxCell>
        <mxCell id="factory-prospect-pool" value="1b2.潜客信息进入厂端潜客池" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-factory" vertex="1">
          <mxGeometry x="100" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-factory" source="factory-update-profile" target="factory-end" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="factory-update-profile" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;8.更新潜客档案状态&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=33;" parent="swimlane-factory" vertex="1">
          <mxGeometry x="100" y="470" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="factory-end" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-factory" vertex="1">
          <mxGeometry x="100" y="650" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-app" value="SUPER APP端（客户）" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="360" y="40" width="320" height="1560" as="geometry" />
        </mxCell>
        <mxCell id="app-start" value="开始" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="100" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-app" source="app-register" target="app-select-dealer" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="app-register" value="1b1.客户注册/使用Super APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="100" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-select-dealer" value="1b3.客户选择门店及顾问" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="100" y="330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-create-order" value="6a2.选择车型创建订单" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="100" y="470" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-pay-deposit" value="6a3.支付定金" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="100" y="580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-order-success" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6a4.订单预订成功&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app" vertex="1">
          <mxGeometry x="100" y="730" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-dealer" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="680" y="40" width="880" height="1560" as="geometry" />
        </mxCell>
        <mxCell id="dealer-start" value="开始" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="380" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-app-check" value="1.潜客是否注册APP？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="350" y="180" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dealer-create-manual" value="1b.销售顾问手动创建潜客档案" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="110" y="190" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-query-prospect" value="1a.销售顾问在厂端潜客池查询到潜客信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="370" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-associated-check" value="2.是否已被同门店销售顾问关联？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="335" y="420" width="210" height="80" as="geometry" />
        </mxCell>
        <mxCell id="xQp2iYfQIZYeqFGliSg8-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="dealer-other-follow" target="dealer-associated-check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dealer-other-follow" value="7a.其他销售顾问跟进" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="680" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-end-early" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="380" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="dealer-edit-profile" target="dealer-follow-up" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-edit-profile" value="2b.编辑潜客档案" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="110" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-follow-up" value="3.销售顾问进行潜客跟进" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="370" y="650" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-update-intention" value="4.更新意向级别 (H/A/B/C)" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="370" y="750" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-test-drive" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;5.试驾管理&lt;br&gt;安排试驾（如需）&lt;/font&gt;&lt;/font&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="370" y="850" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="dealer-intention-check" target="dealer-follow-up" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="610" y="1000" />
              <mxPoint x="610" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-16" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;暂不明确&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UzcyE99-OewxuGR6mmjC-15" vertex="1" connectable="0">
          <mxGeometry x="-0.2227" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-intention-check" value="6.客户意向如何？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="350" y="960" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dealer-mark-defeat" value="6c.销售顾问更新潜客状态为&quot;战败&quot;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="370" y="1150" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="dealer-retry-check" target="dealer-end" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-19" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;否&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UzcyE99-OewxuGR6mmjC-7" vertex="1" connectable="0">
          <mxGeometry x="-0.1536" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="dealer-retry-check" target="dealer-other-follow" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-18" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;是&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UzcyE99-OewxuGR6mmjC-17" vertex="1" connectable="0">
          <mxGeometry x="-0.8656" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-retry-check" value="7.有其他顾问重新跟进？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="350" y="1280" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane-dealer" source="xQp2iYfQIZYeqFGliSg8-3" target="dealer-end" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="150" y="1480" />
            </Array>
            <mxPoint x="150" y="1340" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-end" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;结束&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="380" y="1450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-9" value="6a1.同步订单和潜客信息到门店" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="20" y="730" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xQp2iYfQIZYeqFGliSg8-3" value="6a.订单管理&lt;br&gt;客户下订单" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane-dealer" vertex="1">
          <mxGeometry x="90" y="970" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-check-order" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer" source="dealer-intention-check" target="xQp2iYfQIZYeqFGliSg8-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="220" y="1000" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="label-intention-yes" value="有意向" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-order" vertex="1" connectable="0">
          <mxGeometry x="-0.34" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-app-start-register" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-start" target="app-register" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-select-create" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-select-dealer" target="app-create-order" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-create-pay" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-create-order" target="app-pay-deposit" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-pay-success" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-pay-deposit" target="app-order-success" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-register-factory" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-register" target="factory-prospect-pool" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-start-check" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-start" target="dealer-app-check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-check-manual" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-app-check" target="dealer-create-manual" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-app-check-no" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-manual" vertex="1" connectable="0">
          <mxGeometry x="-0.1875" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-check-query" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-app-check" target="dealer-query-prospect" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-app-check-yes" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-query" vertex="1" connectable="0">
          <mxGeometry x="-0.3919" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-query-associated" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-query-prospect" target="dealer-associated-check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-associated-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-associated-check" target="dealer-end-early" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-associated-yes" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-associated-end" vertex="1" connectable="0">
          <mxGeometry x="-0.2169" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-associated-edit" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-associated-check" target="dealer-edit-profile" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-associated-no" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-associated-edit" vertex="1" connectable="0">
          <mxGeometry x="-0.3841" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-manual-edit" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-create-manual" target="dealer-edit-profile" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-follow-intention" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-follow-up" target="dealer-update-intention" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-intention-test" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-update-intention" target="dealer-test-drive" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-test-check" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-test-drive" target="dealer-intention-check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-check-defeat" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-intention-check" target="dealer-mark-defeat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-intention-no" value="明确无购买意向" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-defeat" vertex="1" connectable="0">
          <mxGeometry x="-0.2045" y="1" relative="1" as="geometry">
            <mxPoint y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-defeat-retry" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-mark-defeat" target="dealer-retry-check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="app-order-success" target="factory-update-profile" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="390" y="800" />
              <mxPoint x="390" y="540" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UzcyE99-OewxuGR6mmjC-9" target="app-order-success" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="app-create-order" target="UzcyE99-OewxuGR6mmjC-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="xQp2iYfQIZYeqFGliSg8-3" target="app-order-success" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="760" y="1040.0344827586207" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xQp2iYfQIZYeqFGliSg8-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="factory-prospect-pool" target="dealer-query-prospect" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="340" />
              <mxPoint x="950" y="340" />
              <mxPoint x="950" y="390" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="Prospect Follow-up Flowchart (English)" id="PROSPECT-FOLLOW-UP-OPTIMIZED-EN">
    <mxGraphModel dx="1753" dy="796" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane-factory-en" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="320" height="1560" as="geometry" />
        </mxCell>
        <mxCell id="factory-prospect-pool-en" value="1b2. Prospect info enters HQ Prospect Pool" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-factory-en" vertex="1">
          <mxGeometry x="100" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-4-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-factory-en" source="factory-update-profile-en" target="factory-end-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="factory-update-profile-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;8. Update Prospect File Status&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=33;" parent="swimlane-factory-en" vertex="1">
          <mxGeometry x="100" y="470" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="factory-end-en" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-factory-en" vertex="1">
          <mxGeometry x="100" y="650" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-app-en" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="360" y="40" width="320" height="1560" as="geometry" />
        </mxCell>
        <mxCell id="app-start-en" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="100" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-1-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-app-en" source="app-register-en" target="app-select-dealer-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="app-register-en" value="1b1. Customer Registers/Uses Super APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="100" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-select-dealer-en" value="1b3. Customer Selects Outlet &amp;amp; SA" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="100" y="330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-create-order-en" value="6a2. Select Vehicle Model to Create Order" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="100" y="470" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-pay-deposit-en" value="6a3. Pay booking fee" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="100" y="580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app-order-success-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6a4. Order Creation Successful&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-app-en" vertex="1">
          <mxGeometry x="100" y="730" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane-dealer-en" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="680" y="40" width="880" height="1560" as="geometry" />
        </mxCell>
        <mxCell id="dealer-start-en" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="380" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-app-check-en" value="1. Has the prospect registered the APP?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="350" y="180" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dealer-create-manual-en" value="1b. SA Manually Creates Prospect File" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="110" y="190" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-query-prospect-en" value="1a. SA researchs Prospect Info in HQ Prospect Pool" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="370" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-associated-check-en" value="2. Already associated by an SA from the same outlet?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="335" y="420" width="210" height="80" as="geometry" />
        </mxCell>
        <mxCell id="xQp2iYfQIZYeqFGliSg8-1-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer-en" source="dealer-other-follow-en" target="dealer-associated-check-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dealer-other-follow-en" value="7a. Other SAs follow up" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="680" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-end-early-en" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="380" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-12-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer-en" source="dealer-edit-profile-en" target="dealer-follow-up-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-edit-profile-en" value="2b. Edit Prospect File" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="110" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-follow-up-en" value="3. SA Follows Up with Prospect" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="370" y="650" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-update-intention-en" value="4. Update Intention Level (H/A/B/C)" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="370" y="750" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dealer-test-drive-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;5. Test Drive Management&lt;br&gt;Arrange Test Drive (if needed)&lt;/font&gt;&lt;/font&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="370" y="850" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-15-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer-en" source="dealer-intention-check-en" target="dealer-follow-up-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="610" y="1000" />
              <mxPoint x="610" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-16-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Uncertain&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UzcyE99-OewxuGR6mmjC-15-en" vertex="1" connectable="0">
          <mxGeometry x="-0.2227" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-intention-check-en" value="6. What is the prosepect&#39;s intention?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="350" y="960" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dealer-mark-defeat-en" value="6c. SA updates prospect status to &amp;quot;Lost&amp;quot;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="370" y="1150" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-7-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane-dealer-en" source="dealer-retry-check-en" target="dealer-end-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-19-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;No&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UzcyE99-OewxuGR6mmjC-7-en" vertex="1" connectable="0">
          <mxGeometry x="-0.1536" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-17-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="swimlane-dealer-en" source="dealer-retry-check-en" target="dealer-other-follow-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-18-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;Yes&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UzcyE99-OewxuGR6mmjC-17-en" vertex="1" connectable="0">
          <mxGeometry x="-0.8656" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-retry-check-en" value="7. Will another SA follow up again?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="350" y="1280" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-8-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane-dealer-en" source="xQp2iYfQIZYeqFGliSg8-3-en" target="dealer-end-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="150" y="1480" />
            </Array>
            <mxPoint x="150" y="1340" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dealer-end-en" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;End&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="380" y="1450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-9-en" value="6a1. Sync order and prospect information to the outlet" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="20" y="730" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xQp2iYfQIZYeqFGliSg8-3-en" value="6a. Order Management&lt;br&gt;SA creats an order" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane-dealer-en" vertex="1">
          <mxGeometry x="90" y="970" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-check-order-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane-dealer-en" source="dealer-intention-check-en" target="xQp2iYfQIZYeqFGliSg8-3-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="220" y="1000" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="label-intention-yes-en" value="Intention to buy" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-order-en" vertex="1" connectable="0">
          <mxGeometry x="-0.34" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-app-start-register-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-start-en" target="app-register-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-select-create-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-select-dealer-en" target="app-create-order-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-create-pay-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-create-order-en" target="app-pay-deposit-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-pay-success-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-pay-deposit-en" target="app-order-success-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-app-register-factory-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-register-en" target="factory-prospect-pool-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-start-check-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-start-en" target="dealer-app-check-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-check-manual-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-app-check-en" target="dealer-create-manual-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-app-check-no-en" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-manual-en" vertex="1" connectable="0">
          <mxGeometry x="-0.1875" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-check-query-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-app-check-en" target="dealer-query-prospect-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-app-check-yes-en" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-query-en" vertex="1" connectable="0">
          <mxGeometry x="-0.3919" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-query-associated-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-query-prospect-en" target="dealer-associated-check-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-associated-end-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-associated-check-en" target="dealer-end-early-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-associated-yes-en" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-associated-end-en" vertex="1" connectable="0">
          <mxGeometry x="-0.2169" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-associated-edit-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-associated-check-en" target="dealer-edit-profile-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-associated-no-en" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-associated-edit-en" vertex="1" connectable="0">
          <mxGeometry x="-0.3841" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-manual-edit-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-create-manual-en" target="dealer-edit-profile-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-follow-intention-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-follow-up-en" target="dealer-update-intention-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-intention-test-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-update-intention-en" target="dealer-test-drive-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-test-check-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-test-drive-en" target="dealer-intention-check-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-dealer-check-defeat-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-intention-check-en" target="dealer-mark-defeat-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-intention-no-en" value="Clearly no intention to buy" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-dealer-check-defeat-en" vertex="1" connectable="0">
          <mxGeometry x="-0.2045" y="1" relative="1" as="geometry">
            <mxPoint y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-dealer-defeat-retry-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="dealer-mark-defeat-en" target="dealer-retry-check-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-5-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="app-order-success-en" target="factory-update-profile-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="390" y="800" />
              <mxPoint x="390" y="540" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-10-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="UzcyE99-OewxuGR6mmjC-9-en" target="app-order-success-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-11-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="app-create-order-en" target="UzcyE99-OewxuGR6mmjC-9-en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="UzcyE99-OewxuGR6mmjC-13-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="xQp2iYfQIZYeqFGliSg8-3-en" target="app-order-success-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="760" y="1040.0344827586207" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xQp2iYfQIZYeqFGliSg8-2-en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="factory-prospect-pool-en" target="dealer-query-prospect-en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="340" />
              <mxPoint x="950" y="340" />
              <mxPoint x="950" y="390" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="2hyZ_A2BmnMbwjrvu94s" name="英文简化版">
    <mxGraphModel dx="2445" dy="1105" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="k4twuxPm8V6R1cCBM5z_-1" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="10" y="9" width="320" height="927" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-2" value="Update prospect info" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-1">
          <mxGeometry x="100" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-6" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="330" y="9" width="320" height="927" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-7" value="Start" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-6">
          <mxGeometry x="100" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-9" value="Registers/Uses Super APP" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-6">
          <mxGeometry x="100" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-14" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;collapsible=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="9" width="880" height="927" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-15" value="Start" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="380" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-71" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-16" target="k4twuxPm8V6R1cCBM5z_-70">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-73" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="k4twuxPm8V6R1cCBM5z_-71">
          <mxGeometry x="-0.2835" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-16" value="Has registered theSuper APP?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="350" y="180" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-17" target="k4twuxPm8V6R1cCBM5z_-70">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="347" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-17" value="Manually Creates Prospect File" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="110" y="190" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-22" value="End" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="669" y="317" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-24" target="k4twuxPm8V6R1cCBM5z_-75">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-24" value="Follow up&amp;amp; Update Interest level" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="370" y="446" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-74" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-70" target="k4twuxPm8V6R1cCBM5z_-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-3" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="k4twuxPm8V6R1cCBM5z_-74">
          <mxGeometry x="0.0048" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-70" target="k4twuxPm8V6R1cCBM5z_-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-5" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="SNhUFJs_imoMOoggCF-S-4">
          <mxGeometry x="-0.4098" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-70" value="Already associated by an SA from the same outlet?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="335" y="307" width="210" height="80" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-75" target="k4twuxPm8V6R1cCBM5z_-77">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-75" value="Test Drive(if needed)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="370" y="563" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-77" target="SNhUFJs_imoMOoggCF-S-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-9" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="SNhUFJs_imoMOoggCF-S-8">
          <mxGeometry x="-0.2535" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-77" target="k4twuxPm8V6R1cCBM5z_-24">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="568" y="717" />
              <mxPoint x="568" y="476" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-12" value="Not sure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="SNhUFJs_imoMOoggCF-S-11">
          <mxGeometry x="-0.4217" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-77" target="SNhUFJs_imoMOoggCF-S-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-18" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="SNhUFJs_imoMOoggCF-S-14">
          <mxGeometry x="-0.1139" y="-4" relative="1" as="geometry">
            <mxPoint x="1" y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-77" value="Does the prospect want to purchase?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="350" y="677" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="k4twuxPm8V6R1cCBM5z_-10" target="k4twuxPm8V6R1cCBM5z_-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-10" value="Book the vehicle" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="-220" y="317" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-7" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;End&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="380" y="817" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="k4twuxPm8V6R1cCBM5z_-14" source="SNhUFJs_imoMOoggCF-S-13" target="SNhUFJs_imoMOoggCF-S-7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="178" y="847" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-13" value="Create sales order" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="k4twuxPm8V6R1cCBM5z_-14">
          <mxGeometry x="105" y="687" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="k4twuxPm8V6R1cCBM5z_-7" target="k4twuxPm8V6R1cCBM5z_-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="k4twuxPm8V6R1cCBM5z_-9" target="k4twuxPm8V6R1cCBM5z_-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="k4twuxPm8V6R1cCBM5z_-15" target="k4twuxPm8V6R1cCBM5z_-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="k4twuxPm8V6R1cCBM5z_-16" target="k4twuxPm8V6R1cCBM5z_-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-50" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="k4twuxPm8V6R1cCBM5z_-49">
          <mxGeometry x="-0.1875" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k4twuxPm8V6R1cCBM5z_-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="k4twuxPm8V6R1cCBM5z_-9" target="k4twuxPm8V6R1cCBM5z_-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SNhUFJs_imoMOoggCF-S-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="SNhUFJs_imoMOoggCF-S-13" target="k4twuxPm8V6R1cCBM5z_-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
