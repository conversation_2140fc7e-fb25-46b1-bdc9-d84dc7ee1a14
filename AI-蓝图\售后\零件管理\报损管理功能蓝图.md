# 报损管理功能蓝图

## 功能概述
本蓝图描述了DMS系统中报损管理的完整业务流程，涵盖从零件破损发现、门店创建报损单、厂端审批、库存标记到最终ERP系统数据同步的全过程。

## 业务流程功能点

| 序号 | 参与角色 | 动作 | 涉及文件（输入/输出） | 描述 |
|------|----------|------|----------------------|------|
| 1 | 门店用户 | 发现零件破损 | 无文件交互 | 门店在收货，使用，搬运等场景下会发现零损坏的情况 |
| 2 | 门店用户 | 新建报损单 | 无文件交互 | 门店根据破损零件情况创建报损申请单，填写破损零件信息、损坏原因、数量等详细信息 |
| 3 | 厂端审批人员 | 审批报损单 | 无文件交互 | 厂端接收门店提交的报损单，对报损申请内容进行审核，判断报损原因是否合理、是否批准该报损申请 |
| 4 | 厂端审批人员 | 查看审批结果 | 无文件交互 | 门店可以查看审批结果是否通过 |
| 5a | 门店用户 | 修改或重做报损单 | 无文件交互 | 当报损单审批被驳回时，门店根据驳回原因修改原报损单内容或重新创建新的报损单 |
| 5b | 门店库存管理员 | 库存标记破损件 | 无文件交互 | 当报损单审批通过后，在门店库存系统中将相应零件标记为破损状态，从可用库存中移除 |
| 6 | ERP系统管理员 | 接收数据信息 | 无文件交互 | 报损信息将同步更新ERP系统中 |
| 7 | 系统管理员 | 监控流程结束 | 无文件交互 | 当ERP系统接收数据完成后，系统自动更新流程状态为完成，整个报损流程正式结束 |

## 业务规则说明

### 审批流程规则
- 报损单必须经过厂端审批才能进入后续处理流程
- 审批驳回的报损单需要门店修改后重新提交审批
- 修改后的报损单将重新进入审批流程，形成循环直到审批通过

### 库存处理规则
- 只有审批通过的报损单才能进行库存标记操作
- 库存标记破损件后，该零件从可用库存中移除
- 破损件标记完成后自动触发ERP系统数据同步

### 系统集成规则
- 审批通过的报损数据自动推送到ERP系统
- ERP系统接收数据后更新相应的库存和财务记录
- 数据同步确保DMS和ERP系统的一致性

### 流程终止规则
- 报损单审批通过并完成库存标记后，流程正常结束
- ERP系统接收数据完成后，整个报损流程彻底完成

## 角色职责

### 门店角色
- **门店用户**：负责发现破损零件、创建和修改报损单
- **门店库存管理员**：负责在库存系统中标记破损零件状态

### 厂端角色  
- **厂端审批人员**：负责审批报损单申请，判断报损的合理性

### 系统角色
- **ERP系统管理员**：负责接收报损数据并同步更新ERP系统记录

## 流程特点

### 简化流程
- 相比叫料流程，报损流程更加简化直接
- 无需复杂的库存判断和分批处理
- 一次性完成从申请到系统同步的全过程

### 审批导向
- 整个流程以厂端审批为核心节点
- 审批结果直接决定后续操作路径
- 驳回后可循环修改直到审批通过

### 数据同步
- 强调DMS和ERP系统的数据一致性
- 确保破损信息在各系统中准确记录
- 支持后续的财务核算和库存管理
