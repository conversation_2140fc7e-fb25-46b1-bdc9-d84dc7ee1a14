# 叫料管理功能蓝图

## 功能概述
本蓝图描述了DMS系统中叫料管理的完整业务流程，涵盖从门店创建叫料单、厂端审批、ERP系统生成采购订单、发货收货到最终库存更新的全过程。

## 业务流程功能点

| 序号 | 参与角色 | 动作 | 涉及文件 | 描述 |
|------|----------|------|----------------------|------|
| 1 | 门店用户 | 创建叫料单 | 无文件交互 | 门店根据实际需求创建叫料申请单，申请零件采购 |
| 2 | 厂端审批人员 | 审批叫料单 | 无文件交互 | HQ接收门店提交的叫料单，对内容进行审核，判断是否批准该叫料申请 |
| 3 | 厂端审批人员 | 查看审批结果 | 无文件交互 | 门店收到来自HQ的审批结果 |
| 4a | 门店用户 | 修改或重做叫料单 | 无文件交互 | 当叫料单审批未通过时，门店用户根据驳回原因修改原叫料单或重新创建新的叫料单 |
| 4b | ERP系统管理员 | 接收数据并生成采购订单 | 无文件交互 | 当叫料单审批通过后，ERP系统自动接收审批通过的叫料数据，并据此生成相应的采购订单 |
| 5 | 厂端库存管理员 | 判断库存情况 | 无文件交互 | HQ根据采购订单包含的零件需求种类和数量检查当前库存状态，判断所需零件是否充足？是否存在缺货的情况？ |
| 6a | 厂端发货人员 | 全部发货 | 无文件交互 | 当库存充足时，按照采购订单要求将所有零件进行打包发货处理 |
| 7a | 门店收货人员 | 收货（全部） | 无文件交互 | 门店接收HQ发出的全部零件货物，核对货物数量和规格是否与订单一致 |
| 8a | 门店库存管理员 | 库存增加（全部） | 无文件交互 | 完成收货后，门店对应的零件库存增加 |
| 6b | 厂端发货人员 | 部分发货 | 无文件交互 | 根据门店的需求，HQ有部分零件缺货时，先发送现有库存中可供应零件部分 |
| 7b | 门店收货人员 | 部分收货 | 无文件交互 | 门店接收HQ发出的部分零件货物，核对实际收到的货物数量后完成收货 |
| 8b | 门店库存管理员 | 库存增加（部分） | 无文件交互 | 完成收货后，门店对应的零件库存增加 |
| 9b | 厂端发货人员 | 判断是否全部发货完成 | 无文件交互 | 门店和HQ可根据订单状态来查看是否全部发货，如还有部分未发货，之后厂端会继续向门店发货 |
|  | 系统管理员 | 监控发货完成状态 | 无文件交互 | 当所有零件发货完成时，系统自动更新订单状态为完成，结束整个叫料流程 |
|  | 厂端发货人员 | 继续发货剩余零件 | 无文件交互 | 当发货未完成时，继续处理剩余未发货的零件，重新进入库存判断和发货流程 |

## 业务规则说明

### 审批流程规则
- 叫料单必须经过厂端审批才能进入后续流程
- 审批未通过的叫料单需要门店修改后重新提交审批
- 修改后的叫料单将重新进入审批流程

### 库存处理规则
- 库存充足：直接全部发货，门店一次性收货完成
- 库存部分缺货：先发现有库存，后续补发缺货部分
- 部分发货模式下，每次收货后都需要判断是否全部发货完成

### 系统集成规则
- 审批通过的叫料数据自动推送到ERP系统
- ERP系统根据叫料数据自动生成采购订单
- 库存更新实时同步到DMS系统

### 流程循环规则
- 部分发货情况下，发货-收货-库存更新形成循环
- 循环持续到所有订单零件全部发货完成
- 每个循环都包含库存判断、发货、收货、库存更新四个步骤

## 角色职责

### 门店角色
- **门店用户**：负责创建和修改叫料单
- **门店收货人员**：负责接收和确认货物
- **门店库存管理员**：负责更新门店库存数据

### 厂端角色  
- **厂端审批人员**：负责审批叫料单申请
- **厂端库存管理员**：负责判断库存状态
- **厂端发货人员**：负责货物发送和发货状态管理

### 系统角色
- **ERP系统管理员**：负责采购订单生成和数据同步
- **系统管理员**：负责流程状态监控和管理
