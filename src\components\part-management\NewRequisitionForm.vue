<template>
  <div class="new-requisition-form">
    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label for="requisitionTitle">叫料清单标题</label>
        <input 
          type="text" 
          id="requisitionTitle" 
          v-model="form.title" 
          required 
          placeholder="请输入叫料清单标题"
        />
      </div>
      
      <div class="form-group">
        <label for="department">申请部门</label>
        <select id="department" v-model="form.department" required>
          <option value="">请选择部门</option>
          <option value="service">售后服务部</option>
          <option value="sales">销售部</option>
          <option value="parts">零件部</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>零件清单</label>
        <div class="parts-list">
          <div v-for="(part, index) in form.parts" :key="index" class="part-item">
            <input 
              type="text" 
              v-model="part.name" 
              placeholder="零件名称" 
              required 
            />
            <input 
              type="number" 
              v-model="part.quantity" 
              placeholder="数量" 
              min="1" 
              required 
            />
            <input 
              type="text" 
              v-model="part.specification" 
              placeholder="规格型号" 
            />
            <button type="button" @click="removePart(index)" class="btn-remove">
              删除
            </button>
          </div>
        </div>
        <button type="button" @click="addPart" class="btn-add">
          添加零件
        </button>
      </div>
      
      <div class="form-group">
        <label for="urgency">紧急程度</label>
        <select id="urgency" v-model="form.urgency" required>
          <option value="normal">普通</option>
          <option value="urgent">紧急</option>
          <option value="critical">非常紧急</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="remarks">备注</label>
        <textarea 
          id="remarks" 
          v-model="form.remarks" 
          rows="3" 
          placeholder="请输入备注信息"
        ></textarea>
      </div>
      
      <div class="form-actions">
        <button type="button" @click="$emit('close')" class="btn btn-cancel">
          取消
        </button>
        <button type="submit" class="btn btn-submit">
          提交申请
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  name: 'NewRequisitionForm',
  emits: ['close'],
  data() {
    return {
      form: {
        title: '',
        department: '',
        parts: [
          { name: '', quantity: 1, specification: '' }
        ],
        urgency: 'normal',
        remarks: ''
      }
    }
  },
  methods: {
    addPart() {
      this.form.parts.push({ name: '', quantity: 1, specification: '' })
    },
    removePart(index) {
      if (this.form.parts.length > 1) {
        this.form.parts.splice(index, 1)
      }
    },
    submitForm() {
      // 这里应该调用API提交表单
      console.log('提交叫料清单:', this.form)
      alert('叫料清单提交成功！')
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.new-requisition-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
}

.parts-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.part-item {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr auto;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  align-items: center;
}

.btn-remove {
  background: #ff4d4f;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.btn-add {
  background: #52c41a;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.btn-cancel {
  background: #f0f0f0;
  color: #333;
}

.btn-submit {
  background: #1890ff;
  color: white;
}

.btn:hover {
  opacity: 0.9;
}
</style>
