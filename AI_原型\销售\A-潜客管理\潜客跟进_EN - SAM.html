<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Outlet Prospect Management - Perodua DMS System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --primary-hover: #40a9ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --bg-white: #ffffff;
            --bg-light: #fafafa;
            --border-color: #e8e8e8;
            --text-primary: #333333;
            --text-secondary: #666666;
        }

        body {
            font-size: 13px;
            color: var(--text-primary);
            background-color: var(--bg-light);
            margin: 0;
            padding: 0;
            display: flex;
        }

        /* 左侧菜单栏样式 */
        .sidebar {
            width: 180px;
            background-color: #1f2937;
            color: #ffffff;
            min-height: 100vh;
            padding: 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #374151;
            background-color: #111827;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .sidebar-nav {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: calc(100vh - 80px); /* 减去头部的高度 */
            scrollbar-width: none; /* Firefox 隐藏滚动条 */
            -ms-overflow-style: none; /* IE and Edge 隐藏滚动条 */
        }

        /* 隐藏滚动条但保留滚动功能 */
        .sidebar-nav::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera 隐藏滚动条 */
        }

        .nav-group {
            margin-bottom: 20px;
        }

        .nav-group-title {
            padding: 8px 16px;
            font-size: 10px;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 6px;
        }

        .nav-item {
            display: block;
            padding: 10px 16px;
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.15s ease;
            border-left: 3px solid transparent;
            font-size: 13px;
        }

        .nav-item:hover {
            background-color: #374151;
            color: #ffffff;
            text-decoration: none;
        }

        .nav-item.active {
            background-color: #1f3a8a;
            color: #ffffff;
            border-left-color: #3b82f6;
            font-weight: 500;
        }

        .nav-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 180px;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .page-container {
            padding: 20px;
            background-color: var(--bg-light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .page-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            color: var(--text-primary);
            flex-shrink: 0;
        }

        /* 筛选区域 */
        .filter-section {
            background-color: #ffffff;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            flex-shrink: 0;
        }

        .form-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .form-control, .form-select {
            font-size: 13px;
            border-color: var(--border-color);
        }

        /* 功能按钮区域 */
        .action-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .btn {
            font-size: 13px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .tab-button {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 8px 16px;
            margin-right: 8px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.15s ease;
            cursor: pointer;
        }

        .tab-button:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
            color: #374151;
        }

        .tab-button.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 可滚动内容区域 */
        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            padding-right: 2px;
            min-height: 0;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .scrollable-content::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }

        /* 表格容器 */
        .table-container {
            background-color: #ffffff;
            width: 100%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            margin-top: 0px;
        }

        .table-responsive {
            width: 100%;
            margin-bottom: 0px;
            overflow-x: auto;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            display: block;
            max-height: 70vh;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .table-responsive::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }

        .table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
            margin-bottom: 0;
            white-space: nowrap;
            font-size: 13px;
            background-color: #ffffff;
            min-width: 2300px;
        }

        @media (max-width: 1500px) {
            .table-responsive {
                min-width: 100%;
            }
            
            .table {
                min-width: 2300px;
            }
        }

        .table th {
            background-color: #f9fafb;
            color: #374151;
            font-size: 12px;
            font-weight: 500;
            border-bottom: none;
            padding: 12px 16px;
            text-align: left;
            letter-spacing: 0.025em;
            text-transform: uppercase;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .table td {
            font-size: 12px;
            border-bottom: 1px solid #f3f4f6;
            padding: 10px 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
            background-color: #ffffff;
            color: #111827;
            height: 48px;
        }

        .table tbody tr {
            transition: background-color 0.15s ease;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table tbody tr:hover td {
            background-color: #f9fafb;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .row-selected {
            background-color: #e6f7ff;
        }

        /* 固定列样式 */
        .sticky-col { 
            position: sticky; 
            right: 0; 
            background-color: #f9fafb !important; 
            z-index: 10; 
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
            border-left: 1px solid #e5e7eb !important;
            overflow: visible !important;
        }

        .first-sticky-col { 
            position: sticky; 
            left: 0; 
            background-color: #ffffff !important; 
            z-index: 20; 
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
        }

        .table th.sticky-col, .table th.first-sticky-col { 
            background-color: #f9fafb !important; 
            z-index: 25;
            position: sticky;
        }

        .table th.sticky-col, .table td.sticky-col {
            text-align: center !important;
            min-width: 140px !important;
            position: sticky !important;
            right: 0 !important;
            background-color: #ffffff !important;
            z-index: 20 !important;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
        }

        .table th.sticky-col {
            background-color: #f9fafb !important;
            z-index: 25 !important;
        }

        .table th.first-sticky-col {
            text-align: center !important;
        }

        .table td.first-sticky-col {
            text-align: center !important;
            background-color: #ffffff !important;
        }

        .table tbody tr:hover td.sticky-col {
            background-color: #f9fafb !important;
        }

        .table tbody tr:hover td.first-sticky-col {
            background-color: #f9fafb !important;
        }

        .row-selected td.sticky-col {
            background-color: #e0e7ff !important;
        }

        .row-selected td.first-sticky-col {
            background-color: #e0f2fe !important;
        }

        .col-checkbox { 
            width: 50px;
            min-width: 50px !important;
        }

        .col-seq { 
            width: 60px;
            min-width: 60px !important;
        }

        /* 标签样式 */
        .tag {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            letter-spacing: 0.025em;
        }

        .tag-success {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .tag-warning {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .tag-error {
            background-color: #fee2e2;
            color: #dc2626;
            border: 1px solid #fca5a5;
        }

        .tag-primary {
            background-color: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .tag-info {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        /* 分页区域 */
        .pagination-section {
            background-color: #ffffff;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            border-radius: 0 0 8px 8px;
        }

        .page-button {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.15s ease;
        }

        .page-button:hover:not(.disabled) {
            background-color: #f3f4f6;
            border-color: #9ca3af;
        }

        .page-button.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f9fafb;
            color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 8px;
            font-size: 12px;
            margin: 1px;
            border-radius: 4px;
            font-weight: 500;
            border: 1px solid;
            transition: all 0.15s ease;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .sticky-col .btn-sm {
            display: inline-flex;
        }

        /* Tooltip样式 */
        .btn-tooltip {
            position: relative;
        }

        .btn-tooltip::after {
            content: attr(data-tooltip);
            position: fixed;
            top: 0;
            left: 0;
            transform: translateX(-50%);
            background-color: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 999999999;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            pointer-events: none;
        }

        .btn-tooltip::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #1f2937;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 999999999;
            pointer-events: none;
        }

        .btn-tooltip:hover::after,
        .btn-tooltip:hover::before {
            opacity: 1;
            visibility: visible;
        }

        .btn-tooltip:hover::after {
            top: calc(var(--tooltip-top) - 45px);
            left: var(--tooltip-left);
        }

        .btn-tooltip:hover::before {
            top: calc(var(--tooltip-top) - 12px);
            left: var(--tooltip-left);
        }

        .btn-success {
            background-color: #10b981;
            border-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        .btn-danger {
            background-color: #ef4444;
            border-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }

        .btn-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }

        .btn-warning {
            background-color: #f59e0b;
            border-color: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
            border-color: #d97706;
        }

        .btn-secondary.disabled {
            background-color: #e5e7eb !important;
            border-color: #d1d5db !important;
            color: #9ca3af !important;
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .btn-secondary.disabled:hover {
            background-color: #e5e7eb !important;
            border-color: #d1d5db !important;
            color: #9ca3af !important;
        }

        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable:hover {
            background-color: #f8fafc;
        }

        .sort-icon {
            margin-left: 5px;
            font-size: 12px;
            color: #6b7280;
            transition: color 0.2s;
        }

        .sort-icon.active {
            color: #3b82f6;
        }

        .sortable:hover .sort-icon {
            color: #3b82f6;
        }

        /* 模态框样式 */
        .modal-title {
            font-size: 13px;
            font-weight: bold;
        }

        .modal-dialog {
            max-width: 800px;
        }

        .modal-dialog.modal-lg {
            max-width: 1000px;
        }

        /* 表单分组样式 */
        .form-group-title {
            font-size: 13px;
            font-weight: bold;
            color: var(--text-primary);
            margin: 20px 0 10px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-group-title:first-child {
            margin-top: 0;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 48px;
            color: var(--border-color);
            margin-bottom: 16px;
        }

        /* 隐藏行样式 */
        .d-none {
            display: none !important;
        }

        /* 排序指示器 */
        .sortable {
            cursor: pointer;
            position: relative;
            user-select: none;
        }

        .sortable:hover {
            background-color: #f0f0f0;
        }

        .sort-icon {
            margin-left: 4px;
            font-size: 10px;
            color: var(--text-secondary);
        }

        .sort-icon.active {
            color: var(--primary-color);
        }

        /* Toast提示样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-left: 4px solid;
            margin-bottom: 10px;
            min-width: 300px;
        }

        .toast.success {
            border-left-color: var(--success-color);
        }

        .toast.error {
            border-left-color: var(--error-color);
        }

        .toast.warning {
            border-left-color: var(--warning-color);
        }

        .toast-header {
            padding: 12px 16px 8px;
            border-bottom: none;
        }

        .toast-body {
            padding: 0 16px 12px;
            font-size: 13px;
        }

        /* Loading状态 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9998;
        }

        .loading-spinner {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .spinner-border {
            width: 2rem;
            height: 2rem;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }

        /* 空数据状态 */
        .empty-data {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-data i {
            font-size: 48px;
            color: var(--border-color);
            margin-bottom: 16px;
        }

        .empty-data .btn {
            margin-top: 16px;
        }

        /* 表单验证错误样式 */
        .is-invalid {
            border-color: var(--error-color);
        }

        .invalid-feedback {
            display: block;
            color: var(--error-color);
            font-size: 12px;
            margin-top: 4px;
        }

        /* 详情弹窗表格样式 */
        .detail-table {
            font-size: 12px !important;
            table-layout: fixed !important;
            width: 100% !important;
            margin-bottom: 0 !important;
            border-collapse: collapse !important;
        }

        .detail-table th,
        .detail-table td {
            padding: 8px 6px !important;
            white-space: normal !important;
            word-wrap: break-word !important;
            vertical-align: top !important;
            overflow: hidden !important;
            border: 1px solid var(--border-color) !important;
            line-height: 1.4 !important;
            text-align: left !important;
        }

        .detail-table th {
            background-color: var(--bg-light) !important;
            font-weight: bold !important;
        }

        .detail-table tbody tr:hover {
            background-color: #f5f5f5 !important;
        }

        /* 潜客意向表格 */
        .intention-table th:nth-child(1),
        .intention-table td:nth-child(1) {
            width: 30% !important;
        }

        .intention-table th:nth-child(2),
        .intention-table td:nth-child(2) {
            width: 40% !important;
        }

        .intention-table th:nth-child(3),
        .intention-table td:nth-child(3) {
            width: 30% !important;
        }

        /* 试驾记录表格 */
        .testdrive-table th:nth-child(1),
        .testdrive-table td:nth-child(1) {
            width: 12% !important;
        }

        .testdrive-table th:nth-child(2),
        .testdrive-table td:nth-child(2) {
            width: 16% !important;
        }

        .testdrive-table th:nth-child(3),
        .testdrive-table td:nth-child(3) {
            width: 20% !important;
        }

        .testdrive-table th:nth-child(4),
        .testdrive-table td:nth-child(4) {
            width: 16% !important;
        }

        .testdrive-table th:nth-child(5),
        .testdrive-table td:nth-child(5) {
            width: 36% !important;
        }

        /* 跟进记录表格 */
        .followup-table th:nth-child(1),
        .followup-table td:nth-child(1) {
            width: 12% !important;
        }

        .followup-table th:nth-child(2),
        .followup-table td:nth-child(2) {
            width: 12% !important;
        }

        .followup-table th:nth-child(3),
        .followup-table td:nth-child(3) {
            width: 16% !important;
        }

        .followup-table th:nth-child(4),
        .followup-table td:nth-child(4) {
            width: 10% !important;
        }

        .followup-table th:nth-child(5),
        .followup-table td:nth-child(5) {
            width: 50% !important;
        }

        /* 变更日志表格 */
        .changelog-table th:nth-child(1),
        .changelog-table td:nth-child(1) {
            width: 18% !important;
        }

        .changelog-table th:nth-child(2),
        .changelog-table td:nth-child(2) {
            width: 20% !important;
        }

        .changelog-table th:nth-child(3),
        .changelog-table td:nth-child(3) {
            width: 20% !important;
        }

        .changelog-table th:nth-child(4),
        .changelog-table td:nth-child(4) {
            width: 16% !important;
        }

        .changelog-table th:nth-child(5),
        .changelog-table td:nth-child(5) {
            width: 26% !important;
        }

        /* 语言切换按钮样式 */
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 8px;
            border: 1px solid #e5e7eb;
        }

        .language-switcher .btn {
            font-size: 12px;
            padding: 6px 12px;
            margin: 0 2px;
            border: 1px solid #d1d5db;
            background: #ffffff;
            color: #6b7280;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .language-switcher .btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .language-switcher .btn.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: #ffffff;
        }
    </style>
</head>
<body>
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>
                <i class="bi bi-speedometer2 me-2"></i>
                <span data-i18n="system.title">Perodua DMS System</span>
            </h3>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.salesManagement">Sales Management</div>
                <a href="#" class="nav-item active">
                    <i class="bi bi-people"></i>
                    <span data-i18n="menu.prospectManagement">Prospect Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-key"></i>
                    <span data-i18n="menu.testDriveManagement">Test Drive Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-clipboard-check"></i>
                    <span data-i18n="menu.orderManagement">Sales Order Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-credit-card"></i>
                    <span data-i18n="menu.paymentManagement">Payment/Refund Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-receipt"></i>
                    <span data-i18n="menu.invoiceManagement">Invoice Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-truck"></i>
                    <span data-i18n="menu.deliveryManagement">Delivery Management</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.afterSalesManagement">Service Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-calendar-check"></i>
                    <span data-i18n="menu.appointmentManagement">Appointment Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-file-earmark-text"></i>
                    <span data-i18n="menu.workOrderManagement">Work Order Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-tools"></i>
                    <span data-i18n="menu.jobAssignmentManagement">Job Dispatch Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-shield-check"></i>
                    <span data-i18n="menu.qualityInspectionManagement">Quality Check Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-calculator"></i>
                    <span data-i18n="menu.settlementManagement">Settlement Management</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.partsManagement">Parts Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-boxes"></i>
                    <span data-i18n="menu.inventoryManagement">Inventory Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-arrow-down-circle"></i>
                    <span data-i18n="menu.inboundManagement">Inbound Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-arrow-up-circle"></i>
                    <span data-i18n="menu.outboundManagement">Outbound Management</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.systemManagement">System Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-person-gear"></i>
                    <span data-i18n="menu.userManagement">User Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-gear"></i>
                    <span data-i18n="menu.systemSettings">System Settings</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- 语言切换按钮 -->
    <div class="language-switcher">
        <button class="btn active" onclick="switchLanguage('zh')" id="btn-zh">中文</button>
        <button class="btn" onclick="switchLanguage('en')" id="btn-en">English</button>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="page-container">
        <h1 class="page-title" data-i18n="page.title">Outlet Prospect Management</h1>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <form id="filterForm">
                <!-- 第一行：4个筛选字段 -->
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.prospectId">Prospect ID</label>
                        <input type="text" class="form-control" id="prospectId" placeholder="Enter Prospect ID" data-i18n-placeholder="filter.prospectId.placeholder">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.customerName">Prospect Name</label>
                        <input type="text" class="form-control" id="customerName" placeholder="Enter Prospect name" data-i18n-placeholder="filter.customerName.placeholder">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.customerPhone">Prospect Phone</label>
                        <input type="text" class="form-control" id="customerPhone" placeholder="Enter Prospect phone number" data-i18n-placeholder="filter.customerPhone.placeholder">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.sourceChannel">Prospect Source</label>
                        <select class="form-select" id="sourceChannel">
                            <option value="" data-i18n="common.all">All</option>
                            <option value="Super APP" data-i18n="source.superApp">Super APP</option>
                            <option value="Outlet Created" data-i18n="source.outletCreated">Outlet Created</option>
                        </select>
                    </div>
                </div>
                <!-- 第二行：2个筛选字段 + 按钮 -->
                <div class="row g-3 mt-1">
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.customerLevel">Customer Intention Level</label>
                        <select class="form-select" id="customerLevel">
                            <option value="" data-i18n="common.all">All</option>
                            <option value="H" data-i18n="level.high">H (High Intention)</option>
                            <option value="A" data-i18n="level.mediumHigh">A (Medium-High Intention)</option>
                            <option value="B" data-i18n="level.mediumLow">B (Medium-Low Intention)</option>
                            <option value="C" data-i18n="level.low">C (Low Intention)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.customerStatus">Customer Status</label>
                        <select class="form-select" id="customerStatus">
                            <option value="" data-i18n="common.all">All</option>
                            <option value="Following" data-i18n="status.following">Following</option>
                            <option value="Lost" data-i18n="status.defeated">Lost</option>
                            <option value="Completed" data-i18n="status.completed">Completed</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-end justify-content-end h-100">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i> <span data-i18n="button.search">Search</span>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="bi bi-arrow-clockwise"></i> <span data-i18n="button.reset">Reset</span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 可滚动内容区域 -->
        <div class="scrollable-content">
            <!-- 功能按钮区域 -->
            <div class="action-section">
                <div>
                    <button class="btn btn-primary me-2" onclick="showAddModal()">
                        <i class="bi bi-plus-circle"></i> <span data-i18n="button.add">Add</span>
                    </button>
                    <button class="btn btn-outline-secondary me-2" onclick="exportData()">
                        <i class="bi bi-download"></i> <span data-i18n="button.export">Export</span>
                    </button>
                </div>
                <div>
                    <button class="tab-button active" onclick="filterByDate('all')" data-i18n="tab.all">All</button>
                    <button class="tab-button" onclick="filterByDate('today')" data-i18n="tab.today">Today</button>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="col-seq first-sticky-col" style="text-align: center; width: 60px;" data-i18n="table.sequence">NO.</th>
                            <th style="width: 120px;" data-i18n="table.prospectId">Prospect ID</th>
                            <th style="width: 130px;" data-i18n="table.source">Source</th>
                            <th style="width: 180px;" data-i18n="table.customerName">Prospect Name</th>
                            <th style="width: 140px;" data-i18n="table.customerPhone">Prospect Phone</th>
                            <th style="width: 80px; text-align: center;" class="sortable" onclick="sortTable('level')" data-i18n="table.intentionLevel">
                                Intention Level
                                <i class="bi bi-arrow-down-up sort-icon" id="sort-level"></i>
                            </th>
                            <th style="width: 150px; text-align: center;" data-i18n="table.customerStatus">Prospect Status</th>
                            <th style="width: 120px;" data-i18n="table.intendedModel">Intended Model</th>
                            <th style="width: 160px;" data-i18n="table.intendedConfig">Intended Variant</th>
                            <th style="width: 120px;" data-i18n="table.intendedColor">Intended Color</th>
                            <th style="width: 100px;" data-i18n="table.advisorId">SA ID</th>
                            <th style="width: 150px;" data-i18n="table.advisorName">SA Name</th>
                            <th style="width: 180px;" data-i18n="table.associatedTime">Associated Time</th>
                            <th style="width: 180px;" data-i18n="table.lastFollowupTime">Last Follow-up Time</th>
                            <th style="width: 180px;" data-i18n="table.nextFollowupTime">Next Follow-up Time</th>
                            <th style="width: 140px; text-align: center;" class="sticky-col" data-i18n="table.actions">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr data-id="P240117001" data-name="Ahmad Rashid" data-phone="************" data-source="Super APP" data-level="H" data-status="Following" data-date="2024-01-15" data-next-followup="2024-01-17 14:20">
                            <td class="col-seq first-sticky-col">1</td>
                            <td>P240117001</td>
                            <td>Super APP</td>
                            <td>Ahmad Rashid</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-error">H</span></td>
                            <td style="text-align: center;"><span class="tag tag-primary">Following</span></td>
                            <td>MYVI</td>
                            <td>1.5L H CVT</td>
                            <td>Blue</td>
                            <td>EMP001</td>
                            <td>Tan Wei Ming</td>
                            <td>2024-01-15 10:30</td>
                            <td>2024-01-16 14:20</td>
                            <td>2024-01-17 14:20</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(1)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(1)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>

                        <tr data-id="P240116002" data-name="Wong Kai Cheng" data-phone="************" data-source="Super APP" data-level="A" data-status="Completed" data-date="2024-01-14" data-next-followup="-">
                            <td class="col-seq first-sticky-col">2</td>
                            <td>P240116002</td>
                            <td>Super APP</td>
                            <td>Wong Kai Cheng</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-warning">A</span></td>
                            <td style="text-align: center;"><span class="tag tag-success">Completed</span></td>
                            <td>MYVI</td>
                            <td>1.5L H CVT</td>
                            <td>Red</td>
                            <td>EMP003</td>
                            <td>Raj Kumar</td>
                            <td>2024-01-14 15:20</td>
                            <td>2024-01-16 11:30</td>
                            <td>-</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(2)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-secondary btn-sm btn-tooltip disabled" data-tooltip="Assign SA" disabled style="opacity: 0.5; cursor: not-allowed;">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240115003" data-name="Nurul Aina" data-phone="************" data-source="Outlet Created" data-level="C" data-status="Lost" data-date="2024-01-13" data-next-followup="-">
                            <td class="col-seq first-sticky-col">3</td>
                            <td>P240115003</td>
                            <td>Outlet Created</td>
                            <td>Nurul Aina</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-info">C</span></td>
                            <td style="text-align: center;"><span class="tag tag-info">Lost</span></td>
                            <td>MYVI</td>
                            <td>1.5A V CVT</td>
                            <td>White</td>
                            <td>EMP004</td>
                            <td>Priya Devi</td>
                            <td>2024-01-13 14:30</td>
                            <td>2024-01-14 09:15</td>
                            <td>-</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(3)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(3)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240117004" data-name="Aminah Binti Hassan" data-phone="************" data-source="Super APP" data-level="A" data-status="Following" data-date="2024-01-15" data-next-followup="2024-01-17 09:30">
                            <td class="col-seq first-sticky-col">4</td>
                            <td>P240117004</td>
                            <td>Super APP</td>
                            <td>Aminah Binti Hassan</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-warning">A</span></td>
                            <td style="text-align: center;"><span class="tag tag-primary">Following</span></td>
                            <td>MYVI</td>
                            <td>1.5L X CVT</td>
                            <td>Gray</td>
                            <td>EMP001</td>
                            <td>Tan Wei Ming</td>
                            <td>2024-01-15 11:20</td>
                            <td>2024-01-16 09:30</td>
                            <td>2024-01-17 09:30</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(4)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(4)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240116005" data-name="Kumar Murugan" data-phone="************" data-source="Outlet Created" data-level="H" data-status="Following" data-date="2024-01-14" data-next-followup="2024-01-17 10:20">
                            <td class="col-seq first-sticky-col">5</td>
                            <td>P240116005</td>
                            <td>Outlet Created</td>
                            <td>Kumar Murugan</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-error">H</span></td>
                            <td style="text-align: center;"><span class="tag tag-primary">Following</span></td>
                            <td>MYVI</td>
                            <td>1.5L H CVT</td>
                            <td>Red</td>
                            <td>EMP002</td>
                            <td>Lim Ah Hock</td>
                            <td>2024-01-14 16:45</td>
                            <td>2024-01-15 10:20</td>
                            <td>2024-01-17 10:20</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(5)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(5)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240115006" data-name="Fatimah Zahra" data-phone="************" data-source="Super APP" data-level="B" data-status="Following" data-date="2024-01-13" data-next-followup="2024-01-17 15:45">
                            <td class="col-seq first-sticky-col">6</td>
                            <td>P240115006</td>
                            <td>Super APP</td>
                            <td>Fatimah Zahra</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-warning">B</span></td>
                            <td style="text-align: center;"><span class="tag tag-primary">Following</span></td>
                            <td>MYVI</td>
                            <td>1.5A V CVT</td>
                            <td>Silver</td>
                            <td>EMP003</td>
                            <td>Raj Kumar</td>
                            <td>2024-01-13 08:30</td>
                            <td>2024-01-14 15:45</td>
                            <td>2024-01-17 15:45</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(6)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(6)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240114007" data-name="Lee Wei Jian" data-phone="************" data-source="Outlet Created" data-level="C" data-status="Completed" data-date="2024-01-12" data-next-followup="-">
                            <td class="col-seq first-sticky-col">7</td>
                            <td>P240114007</td>
                            <td>Outlet Created</td>
                            <td>Lee Wei Jian</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-info">C</span></td>
                            <td style="text-align: center;"><span class="tag tag-success">Completed</span></td>
                            <td>MYVI</td>
                            <td>1.5L X CVT</td>
                            <td>Blue</td>
                            <td>EMP004</td>
                            <td>Priya Devi</td>
                            <td>2024-01-12 13:15</td>
                            <td>2024-01-15 11:20</td>
                            <td>-</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(7)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-secondary btn-sm btn-tooltip disabled" data-tooltip="Assign SA" disabled style="opacity: 0.5; cursor: not-allowed;">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240117008" data-name="Muhammad Hafiz" data-phone="************" data-source="Super APP" data-level="H" data-status="Following" data-date="2024-01-15" data-next-followup="2024-01-17 16:30">
                            <td class="col-seq first-sticky-col">8</td>
                            <td>P240117008</td>
                            <td>Super APP</td>
                            <td>Muhammad Hafiz</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-error">H</span></td>
                            <td style="text-align: center;"><span class="tag tag-primary">Following</span></td>
                            <td>MYVI</td>
                            <td>1.5L H CVT</td>
                            <td>Black</td>
                            <td>EMP001</td>
                            <td>Tan Wei Ming</td>
                            <td>2024-01-15 14:00</td>
                            <td>2024-01-16 16:30</td>
                            <td>2024-01-17 16:30</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(8)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(8)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240113009" data-name="Deepa Krishnan" data-phone="************" data-source="Outlet Created" data-level="A" data-status="Lost" data-date="2024-01-11" data-next-followup="-">
                            <td class="col-seq first-sticky-col">9</td>
                            <td>P240113009</td>
                            <td>Outlet Created</td>
                            <td>Deepa Krishnan</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-warning">A</span></td>
                            <td style="text-align: center;"><span class="tag tag-info">Lost</span></td>
                            <td>MYVI</td>
                            <td>1.5A V CVT</td>
                            <td>White</td>
                            <td>EMP002</td>
                            <td>Lim Ah Hock</td>
                            <td>2024-01-11 10:45</td>
                            <td>2024-01-12 14:20</td>
                            <td>-</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(9)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(9)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-id="P240116010" data-name="Chong Mei Ling" data-phone="************" data-source="Super APP" data-level="B" data-status="Following" data-date="2024-01-14" data-next-followup="2024-01-17 13:45">
                            <td class="col-seq first-sticky-col">10</td>
                            <td>P240116010</td>
                            <td>Super APP</td>
                            <td>Chong Mei Ling</td>
                            <td>************</td>
                            <td style="text-align: center;"><span class="tag tag-warning">B</span></td>
                            <td style="text-align: center;"><span class="tag tag-primary">Following</span></td>
                            <td>MYVI</td>
                            <td>1.5L X CVT</td>
                            <td>Gray</td>
                            <td>EMP003</td>
                            <td>Raj Kumar</td>
                            <td>2024-01-14 09:20</td>
                            <td>2024-01-15 13:45</td>
                            <td>2024-01-17 13:45</td>
                            <td class="sticky-col">
                                <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="Details" onclick="viewDetail(10)">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm btn-tooltip" data-tooltip="Assign SA" onclick="showAssignSAModal(10)">
                                    <i class="bi bi-person-plus"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页区域 -->
            <div class="pagination-section">
                <div>
                    <span data-i18n="pagination.pageSize">Items per page</span>
                    <select class="form-select d-inline-block mx-2" style="width: auto;" onchange="changePageSize(this.value)">
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                    </select>
                    <span data-i18n="pagination.records">items, total <span id="totalCount">10</span> records</span>
                </div>
                <div>
                    <button class="page-button" id="prevPage" onclick="changePage('prev')" data-i18n="pagination.previous">Previous</button>
                    <button class="page-button active" id="currentPage">1</button>
                    <button class="page-button" id="nextPage" onclick="changePage('next')" data-i18n="pagination.next">Next</button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- 新增潜客模态框 -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-i18n="modal.addProspect">Add Prospect</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 潜客查询区域 -->
                    <div class="border rounded p-3 mb-4" style="background-color: #f8f9fa; border: 2px solid #007bff !important;">
                        <div class="form-group-title" style="color: #007bff; margin-top: 0;">Prospect Search</div>
                        <div class="row g-3">
                            <div class="col-md-5">
                                <label class="form-label">Prospect Name</label>
                                <input type="text" class="form-control" id="searchCustomerName" placeholder="Enter prospect name">
                            </div>
                            <div class="col-md-5">
                                <label class="form-label">Prospect Phone</label>
                                <input type="text" class="form-control" id="searchCustomerPhone" placeholder="Enter phone number">
                            </div>
                            <div class="col-md-2">
                                <div class="d-flex align-items-end h-100">
                                    <button type="button" class="btn btn-primary w-100" onclick="searchExistingCustomer()">Search</button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Customers registered with Super APP can be researched for Prospect profiles</small>
                        </div>
                    </div>
                    
                    <!-- 新增潜客表单 -->
                    <form id="addCustomerForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label" data-i18n="form.prospectId">Prospect ID</label>
                                <input type="text" class="form-control" id="addProspectId" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Prospect Name</label>
                                <input type="text" class="form-control" id="addCustomerName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Prospect Phone</label>
                                <input type="tel" class="form-control" id="addCustomerPhone" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Intention Level</label>
                                <select class="form-select" id="addIntentionLevel" required>
                                    <option value="">Select</option>
                                    <option value="H">H (High Intention)</option>
                                    <option value="A">A (Medium-High Intention)</option>
                                    <option value="B">B (Medium-Low Intention)</option>
                                    <option value="C">C (Low Intention)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ID Type</label>
                                <select class="form-select" id="addIdType">
                                    <option value="">Select</option>
                                    <option value="身份证">MyKad</option>
                                    <option value="护照">Passport</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ID Number</label>
                                <input type="text" class="form-control" id="addIdNumber">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" id="addEmail">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-i18n="button.cancel">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveCustomer()" data-i18n="button.create">Create</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 潜客跟进模态框 -->
    <div class="modal fade" id="followUpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-i18n="modal.prospectFollowUp">Prospect Follow-up</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="followUpForm">
                        <!-- 潜客信息part -->
                        <div class="form-group-title">Prospect Information</div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label" data-i18n="form.prospectId">Prospect ID</label>
                                <input type="text" class="form-control" id="followUpProspectId" value="P240117001" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Prospect Name</label>
                                <input type="text" class="form-control" id="followUpCustomerName" value="Ahmad Rashid" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Prospect Phone</label>
                                <input type="tel" class="form-control" id="followUpCustomerPhone" value="************" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*ID Type</label>
                                <select class="form-select" required>
                                    <option value="身份证" selected>MyKad</option>
                                    <option value="护照">Passport</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*ID Number</label>
                                <input type="text" class="form-control" value="850123-02-1234" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Email</label>
                                <input type="email" class="form-control" value="<EMAIL>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Area</label>
                                <select class="form-select">
                                    <option value="Kuala Lumpur" selected>Kuala Lumpur</option>
                                    <option value="Penang">Penang</option>
                                    <option value="Johor Bahru">Johor Bahru</option>
                                </select>
                            </div>
                        </div>

                        <!-- 潜客意向part -->
                        <div class="form-group-title">Prospect Intention</div>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">*Intended Model</label>
                                <select class="form-select" required>
                                    <option value="MYVI" selected>MYVI</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">*Intended Variant</label>
                                <select class="form-select" required>
                                    <option value="1.5L H CVT" selected>1.5L H CVT</option>
                                    <option value="1.5A V CVT">1.5A V CVT</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">*Intended Color</label>
                                <select class="form-select" required>
                                    <option value="蓝色" selected>Blue</option>
                                    <option value="银色">Silver</option>
                                    <option value="红色">Red</option>
                                </select>
                            </div>
                        </div>

                        <!-- 跟进记录part -->
                        <div class="form-group-title">Follow-up Record</div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Sales Advisor</label>
                                <input type="text" class="form-control" id="followUpConsultant" value="Tan Wei Ming" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Follow-up Method</label>
                                <select class="form-select" id="followUpMethod" required>
                                    <option value="">Select</option>
                                    <option value="电话">Phone Call</option>
                                    <option value="社交软件">Social Media</option>
                                    <option value="到店面谈">In-Store Visit</option>
                                    <option value="上门拜访">Home Visit</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Follow-up Time</label>
                                <input type="datetime-local" class="form-control" id="followUpTime" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Intention Level</label>
                                <select class="form-select" id="intentionLevel" required>
                                    <option value="">Select</option>
                                    <option value="H">H (High Intention)</option>
                                    <option value="A">A (Medium-High Intention)</option>
                                    <option value="B">B (Medium-Low Intention)</option>
                                    <option value="C">C (Low Intention)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">*Next Follow-up Time</label>
                                <input type="datetime-local" class="form-control" id="nextFollowUpTime" required>
                            </div>
                            <div class="col-md-6">
                                <div class="form-label text-muted">
                                    <small id="followUpTimeTip" data-i18n="tip.selectIntentionFirst">Please select intention level and follow-up time first</small>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">*Follow-up Details</label>
                                <textarea class="form-control" id="followUpContent" rows="4" placeholder="Describe the follow-up details..." required></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-i18n="button.cancel">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveFollowUp()" data-i18n="button.save">Save</button>
                </div>
            </div>
        </div>
    </div>



                <!-- 无意向潜客申请模态框 -->
    <div class="modal fade" id="defeatModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-i18n="modal.markAsLost">Mark Prospect as Lost</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="defeatForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Prospect Name</label>
                                <input type="text" class="form-control" id="defeatCustomerName" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Prospect Phone</label>
                                <input type="text" class="form-control" id="defeatCustomerPhone" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Current Sales Advisor</label>
                                <input type="text" class="form-control" id="defeatConsultant" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Application Time</label>
                                <input type="text" class="form-control" id="defeatApplyTime" readonly>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">*Lost Reason</label>
                                <select class="form-select" id="defeatReason" required>
                                    <option value="">Please select lost reason</option>
                                    <option value="1">Price Reason - Customer thinks price is too high</option>
                                    <option value="2">Competitor Reason - Customer chose competitor</option>
                                    <option value="3">Stock Reason - Unable to provide customer's required variant</option>
                                    <option value="4">Service Reason - Customer dissatisfied with service</option>
                                    <option value="5">Sales Skill Insufficient - Unable to effectively persuade</option>
                                    <option value="6">Area Reason - Customer purchasing in different area</option>
                                    <option value="7">False Customer - Not genuine purchase intention</option>
                                    <option value="8">Other Reasons</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">*Detailed Description</label>
                                <textarea class="form-control" rows="4" id="defeatDescription" placeholder="Please describe the lost reason in detail..." required></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-i18n="button.cancel">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitDefeatRequest()" data-i18n="button.submit">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 潜客档案详情模态框 -->
    <div class="modal fade" id="customerDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-i18n="modal.prospectDetails">Prospect Profile Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 潜客信息part -->
                    <div class="form-group-title" data-i18n="detail.prospectInfo">Prospect Information</div>
                    <div class="row g-3 mb-4">
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.prospectId">Prospect ID</label>
                            <input type="text" class="form-control" id="detailProspectId" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.prospectSource">Prospect Source</label>
                            <input type="text" class="form-control" id="detailSource" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.prospectStatus">Prospect Status</label>
                            <input type="text" class="form-control" id="detailStatus" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.prospectName">Prospect Name</label>
                            <input type="text" class="form-control" id="detailName" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.prospectPhone">Prospect Phone</label>
                            <input type="text" class="form-control" id="detailPhone" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.idType">ID Type</label>
                            <input type="text" class="form-control" id="detailIdType" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.idNumber">ID Number</label>
                            <input type="text" class="form-control" id="detailIdNumber" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.email">Email</label>
                            <input type="text" class="form-control" id="detailEmail" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label" data-i18n="detail.area">Area</label>
                            <input type="text" class="form-control" id="detailRegion" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label" data-i18n="detail.address">Address</label>
                            <input type="text" class="form-control" id="detailAddress" readonly>
                        </div>
                    </div>

                    <!-- 潜客意向part -->
                    <div class="form-group-title" data-i18n="detail.prospectIntention">Prospect Intention</div>
                    <div class="table-responsive mb-4" style="max-height: 200px;">
                        <table class="detail-table intention-table">
                            <thead>
                                <tr>
                                    <th data-i18n="detail.intendedModel">Intended Model</th>
                                    <th data-i18n="detail.intendedVariant">Intended Variant</th>
                                    <th data-i18n="detail.intendedColor">Intended Color</th>
                                </tr>
                            </thead>
                            <tbody id="intentionTableBody">
                                <tr>
                                    <td>MYVI</td>
                                    <td>1.5L H CVT</td>
                                    <td>Blue</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 试驾记录part -->
                    <div class="form-group-title" data-i18n="detail.testDriveRecord">Test Drive Record</div>
                    <div class="table-responsive mb-4" style="max-height: 200px;">
                        <table class="detail-table testdrive-table">
                            <thead>
                                <tr>
                                    <th data-i18n="detail.testDriver">Test Driver</th>
                                    <th data-i18n="detail.phone">Phone</th>
                                    <th data-i18n="detail.model">Model</th>
                                    <th data-i18n="detail.time">Time</th>
                                    <th data-i18n="detail.feedback">Feedback</th>
                                </tr>
                            </thead>
                            <tbody id="testDriveTableBody">
                                <tr>
                                    <td>Ahmad Rashid</td>
                                    <td>************</td>
                                    <td>MYVI 1.5L H CVT</td>
                                    <td>2024-01-10 14:30</td>
                                    <td>Good driving experience, satisfied with power performance</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 跟进记录part -->
                    <div class="form-group-title" data-i18n="detail.followUpRecord">Follow-up Record</div>
                    <div class="table-responsive mb-4" style="max-height: 200px;">
                        <table class="detail-table followup-table">
                            <thead>
                                <tr>
                                    <th data-i18n="detail.salesAdvisor">Sales Advisor</th>
                                    <th data-i18n="detail.followUpMethod">Follow-up Method</th>
                                    <th data-i18n="detail.followUpTime">Follow-up Time</th>
                                </tr>
                            </thead>
                            <tbody id="followUpTableBody">
                                <tr>
                                    <td>Tan Wei Ming</td>
                                    <td>Phone Call</td>
                                    <td>2024-01-16 14:20</td>
                                </tr>
                                <tr>
                                    <td>Tan Wei Ming</td>
                                    <td>In-Store Visit</td>
                                    <td>2024-01-12 10:15</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                                            <!-- 变更日志part -->
                        <div class="form-group-title" data-i18n="detail.operationLog">Operation Log</div>
                    <div class="table-responsive" style="max-height: 200px;">
                        <table class="detail-table changelog-table">
                            <thead>
                                <tr>
                                    <th data-i18n="detail.changeContent">Change Content</th>
                                    <th data-i18n="detail.originalInfo">Original Information</th>
                                    <th data-i18n="detail.changedInfo">Changed Information</th>
                                    <th data-i18n="detail.operator">Operator</th>
                                    <th data-i18n="detail.operationTime">Operation Time</th>
                                </tr>
                            </thead>
                            <tbody id="changeLogTableBody">
                                <tr>
                                    <td>Change Sales Advisor</td>
                                    <td>Lim Ah Hock(EMP002)</td>
                                    <td>Tan Wei Ming(EMP001)</td>
                                    <td>Administrator</td>
                                    <td>2024-01-15 09:30</td>
                                </tr>
                                <tr>
                                    <td>Update Intention Level</td>
                                    <td>A Level</td>
                                    <td>H Level</td>
                                    <td>Tan Wei Ming</td>
                                    <td>2024-01-16 14:20</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-i18n="button.close">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分配顾问模态框 -->
    <div class="modal fade" id="assignSAModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-i18n="modal.assignSA">Assign SA</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="assignSAForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Prospect Name</label>
                                <input type="text" class="form-control" id="assignProspectName" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Prospect Phone</label>
                                <input type="text" class="form-control" id="assignProspectPhone" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">Current SA</label>
                                <input type="text" class="form-control" id="currentSA" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">*Reassign to SA</label>
                                <select class="form-select" id="newSA" required>
                                    <option value="">Select SA</option>
                                    <option value="EMP001">EMP001 - Tan Wei Ming</option>
                                    <option value="EMP002">EMP002 - Lim Ah Hock</option>
                                    <option value="EMP003">EMP003 - Raj Kumar</option>
                                    <option value="EMP004">EMP004 - Priya Devi</option>
                                    <option value="EMP005">EMP005 - Ahmad Rahman</option>
                                    <option value="EMP006">EMP006 - Siti Nurhaliza</option>
                                </select>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">Reassignment Reason</label>
                                <textarea class="form-control" id="assignmentReason" rows="3" placeholder="Enter reason for reassignment (optional)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-i18n="button.cancel">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveAssignment()" data-i18n="button.assign">Assign</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Loading遮罩 -->
    <div class="loading-overlay d-none" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status"></div>
            <div class="mt-2" data-i18n="detail.loading">Loading...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 国际化语言包
        const i18nResources = {
            zh: {
                'page.title': '门店潜客管理',
                'filter.customerName': '潜客名称',
                'filter.customerName.placeholder': '请输入潜客名称',
                'filter.customerPhone': '潜客手机号',
                'filter.customerPhone.placeholder': '请输入手机号',
                'filter.prospectId': '潜客ID',
                'filter.prospectId.placeholder': '请输入潜客ID',
                'filter.sourceChannel': '潜客来源',
                'filter.customerLevel': '潜客意向级别',
                'filter.customerStatus': '潜客状态',
                'common.all': '全部',
                'common.storeCreated': '门店创建',
                'level.high': 'H（高意向）',
                'level.mediumHigh': 'A（中高意向）',
                'level.mediumLow': 'B（中低意向）',
                'level.low': 'C（低意向）',
                'status.following': '跟进中',
                'status.defeated': '无意向',
                'status.completed': '已成交',
                'button.search': '查询',
                'button.reset': '重置',
                'button.add': '新增',
                'button.export': '导出',
                'tab.all': '全部',
                'tab.today': '今日',
                'table.sequence': '序号',
                'table.prospectId': '潜客ID',
                'table.source': '潜客来源',
                'table.customerName': '潜客名称',
                'table.customerPhone': '潜客手机号',
                'table.intentionLevel': '意向级别',
                'table.customerStatus': '潜客状态',
                'table.intendedModel': '意向车型',
                'table.intendedConfig': '意向配置',
                'table.intendedColor': '意向颜色',
                'table.advisorId': '所属顾问工号',
                'table.advisorName': '所属顾问姓名',
                'table.associatedTime': '关联潜客时间',
                'table.lastFollowupTime': '上次跟进时间',
                'table.nextFollowupTime': '下次跟进时间',
                'table.actions': '操作',
                'menu.salesManagement': '销售管理',
                'menu.prospectManagement': '潜客管理',
                'menu.testDriveManagement': '试驾管理',
                'menu.orderManagement': '订单管理',
                'menu.paymentManagement': '收款管理',
                'menu.invoiceManagement': '发票管理',
                'menu.deliveryManagement': '交车管理',
                'menu.afterSalesManagement': '售后管理',
                'menu.appointmentManagement': '预约管理',
                'menu.workOrderManagement': '工单管理',
                'menu.jobAssignmentManagement': '派工管理',
                'menu.qualityInspectionManagement': '质检管理',
                'menu.settlementManagement': '结算管理',
                'menu.partsManagement': '零配件管理',
                'menu.inventoryManagement': '库存管理',
                'menu.inboundManagement': '入库管理',
                'menu.outboundManagement': '出库管理',
                'menu.systemManagement': '系统管理',
                'menu.userManagement': '用户管理',
                'menu.systemSettings': '系统设置',
                'system.title': 'DMS 系统',
                'pagination.pageSize': '每页显示',
                'pagination.records': '条，共 ',
                'pagination.previous': '上一页',
                'pagination.next': '下一页',
                'tooltip.details': '详情',
                'tooltip.followUp': '跟进',
                'tooltip.markAsLost': '标记无意向',
                'tooltip.assignSA': '分配顾问',
                'message.followUpSaved': '跟进记录保存成功！',
                'message.fillCompleteFollowUp': '请填写完整的跟进信息',
                'message.markAsLostSubmitted': '标记无意向提交成功！',
                'message.fillCompleteLostInfo': '请填写完整的无意向信息',
                'message.customerCreated': '客户创建成功！',
                'message.enterNameOrPhone': '请输入潜客名称或手机号',
                'message.searchRequiresAPI': '搜索功能需要后端API支持',
                'message.exportRequiresAPI': '导出功能需要后端API支持',
                'tip.selectIntentionFirst': '请先选择意向级别和跟进时间',
                'tip.highIntention': '高意向客户建议3天内跟进',
                'tip.mediumHighIntention': '中高意向客户建议7天内跟进',
                'tip.mediumLowIntention': '中低意向客户建议14天内跟进',
                'tip.lowIntention': '低意向客户建议30天内跟进',
                'status.following.label': '跟进中',
                'status.lost.label': '无意向',
                'status.completed.label': '已成交',
                'modal.addProspect': '新增潜客',
                'modal.prospectFollowUp': '潜客跟进',
                'modal.markAsLost': '标记潜客无意向',
                'modal.prospectDetails': '潜客档案详情',
                'modal.assignSA': '分配顾问',
                'button.cancel': '取消',
                'button.create': '创建',
                'button.save': '保存',
                'button.submit': '提交',
                'button.close': '关闭',
                'button.assign': '分配',
                'detail.prospectInfo': '潜客信息',
                'detail.prospectIntention': '潜客意向',
                'detail.testDriveRecord': '试驾记录',
                'detail.followUpRecord': '跟进记录',
                'detail.operationLog': '变更日志',
                'detail.loading': '加载中...',
                'detail.prospectId': '潜客ID',
                'detail.prospectSource': '潜客来源',
                'detail.prospectStatus': '潜客状态',
                'detail.prospectName': '潜客名称',
                'detail.prospectPhone': '潜客手机号',
                'detail.idType': '证件类型',
                'detail.idNumber': '证件号码',
                'detail.email': '邮箱',
                'detail.area': '地区',
                'detail.address': '地址',
                'detail.intendedModel': '意向车型',
                'detail.intendedVariant': '意向配置',
                'detail.intendedColor': '意向颜色',
                'detail.testDriver': '试驾人',
                'detail.phone': '手机号',
                'detail.model': '车型',
                'detail.time': '时间',
                'detail.feedback': '反馈',
                'detail.salesAdvisor': '销售顾问',
                'detail.followUpMethod': '跟进方式',
                'detail.followUpTime': '跟进时间',
                'detail.changeContent': '变更内容',
                'detail.originalInfo': '原始信息',
                'detail.changedInfo': '变更信息',
                'detail.operator': '操作人',
                'detail.operationTime': '操作时间',
                'form.prospectId': '潜客ID',
                'data.idCard': '身份证',
                'data.following': '跟进中',
                'data.kualaLumpur': '吉隆坡',
                'data.cityCenter': '市中心',
                'prospects.prospect': '潜客',
                'source.superApp': 'Super APP',
                'source.outletCreated': '门店创建'
            },
            en: {
                'page.title': 'Outlet Prospect Management',
                'filter.customerName': 'Prospect Name',
                'filter.customerName.placeholder': 'Enter Prospect name',
                'filter.customerPhone': 'Prospect Phone',
                'filter.customerPhone.placeholder': 'Enter Prospect phone number',
                'filter.prospectId': 'Prospect ID',
                'filter.prospectId.placeholder': 'Enter Prospect ID',
                'filter.sourceChannel': 'Prospect Source',
                'filter.customerLevel': 'Prospect Intention Level',
                'filter.customerStatus': 'Prospect Status',
                'common.all': 'All',
                'common.storeCreated': 'Outlet Created',
                'level.high': 'H (High Intention)',
                'level.mediumHigh': 'A (Medium-High Intention)',
                'level.mediumLow': 'B (Medium-Low Intention)',
                'level.low': 'C (Low Intention)',
                'status.following': 'Following',
                'status.defeated': 'Lost',
                'status.completed': 'Closed (Won)',
                'button.search': 'Search',
                'button.reset': 'Reset',
                'button.add': 'Add',
                'button.export': 'Export',
                'tab.all': 'All',
                'tab.today': 'Today',
                'table.sequence': 'NO.',
                'table.prospectId': 'Prospect ID',
                'table.source': 'Prospect Source',
                'table.customerName': 'Prospect Name',
                'table.customerPhone': 'Prospect Phone',
                'table.intentionLevel': 'Intention Level',
                'table.customerStatus': 'Prospect Status',
                'table.intendedModel': 'Intended Model',
                'table.intendedConfig': 'Intended Variant',
                'table.intendedColor': 'Intended Color',
                'table.advisorId': 'SA ID',
                'table.advisorName': 'SA Name',
                'table.associatedTime': 'Prospect Associated Time',
                'table.lastFollowupTime': 'Last Follow-up Time',
                'table.nextFollowupTime': 'Next Follow-up Time',
                'table.actions': 'Actions',
                'menu.salesManagement': 'Sales Management',
                'menu.prospectManagement': 'Prospect Management',
                'menu.testDriveManagement': 'Test Drive Management',
                'menu.orderManagement': 'Sales Order Management',
                'menu.paymentManagement': 'Payment/Refund Management',
                'menu.invoiceManagement': 'Invoice Management',
                'menu.deliveryManagement': 'Delivery Management',
                'menu.afterSalesManagement': 'Service Management',
                'menu.appointmentManagement': 'Appointment Management',
                'menu.workOrderManagement': 'Work Order Management',
                'menu.jobAssignmentManagement': 'Job Dispatch Management',
                'menu.qualityInspectionManagement': 'Quality Check Management',
                'menu.settlementManagement': 'Settlement Management',
                'menu.partsManagement': 'Parts Management',
                'menu.inventoryManagement': 'Inventory Management',
                'menu.inboundManagement': 'Inbound Management',
                'menu.outboundManagement': 'Outbound Management',
                'menu.systemManagement': 'System Management',
                'menu.userManagement': 'User Management',
                'menu.systemSettings': 'System Settings',
                'system.title': 'Perodua DMS System',
                'pagination.pageSize': 'Items per page',
                'pagination.records': 'items, total',
                'pagination.previous': 'Previous',
                'pagination.next': 'Next',
                'tooltip.details': 'Details',
                'tooltip.followUp': 'Follow Up',
                'tooltip.markAsLost': 'Mark as Lost',
                'tooltip.assignSA': 'Assign SA',
                'message.followUpSaved': 'Follow-up record saved successfully!',
                'message.fillCompleteFollowUp': 'Please fill in complete follow-up information',
                'message.markAsLostSubmitted': 'Mark as lost submitted successfully!',
                'message.fillCompleteLostInfo': 'Please fill in complete lost information',
                'message.customerCreated': 'Customer created successfully!',
                'message.enterNameOrPhone': 'Please enter prospect name or phone number',
                'message.searchRequiresAPI': 'Search function requires backend API support',
                'message.exportRequiresAPI': 'Export function requires backend API support',
                'tip.selectIntentionFirst': 'Please select intention level and follow-up time first',
                'tip.highIntention': 'High intention customers recommended follow-up within 3 days',
                'tip.mediumHighIntention': 'Medium-high intention customers recommended follow-up within 7 days',
                'tip.mediumLowIntention': 'Medium-low intention customers recommended follow-up within 14 days',
                'tip.lowIntention': 'Low intention customers recommended follow-up within 30 days',
                'status.following.label': 'Following',
                'status.lost.label': 'Lost',
                'status.completed.label': 'Closed (Won)',
                'modal.addProspect': 'Add New Prospect',
                'modal.prospectFollowUp': 'Follow Up Prospect',
                'modal.markAsLost': 'Mark Prospect as Lost',
                'modal.prospectDetails': 'Prospect Profile Details',
                'modal.assignSA': 'Assign SA',
                'button.cancel': 'Cancel',
                'button.create': 'Create',
                'button.save': 'Save',
                'button.submit': 'Submit',
                'button.close': 'Close',
                'button.assign': 'Assign',
                'detail.prospectInfo': 'Prospect Information',
                'detail.prospectIntention': 'Prospect Intention',
                'detail.testDriveRecord': 'Test Drive Record',
                'detail.followUpRecord': 'Follow-up Record',
                'detail.operationLog': 'Operation Log',
                'detail.loading': 'Loading...',
                'detail.prospectId': 'Prospect ID',
                'detail.prospectSource': 'Prospect Source',
                'detail.prospectStatus': 'Prospect Status',
                'detail.prospectName': 'Prospect Name',
                'detail.prospectPhone': 'Prospect Phone',
                'detail.idType': 'ID Type',
                'detail.idNumber': 'ID Number',
                'detail.email': 'Email',
                'detail.area': 'Area',
                'detail.address': 'Address',
                'detail.intendedModel': 'Intended Model',
                'detail.intendedVariant': 'Intended Variant',
                'detail.intendedColor': 'Intended Color',
                'detail.testDriver': 'Test Driver',
                'detail.phone': 'Phone',
                'detail.model': 'Model',
                'detail.time': 'Time',
                'detail.feedback': 'Feedback',
                'detail.salesAdvisor': 'Sales Advisor',
                'detail.followUpMethod': 'Follow-up Method',
                'detail.followUpTime': 'Follow-up Time',
                'detail.changeContent': 'Change Content',
                'detail.originalInfo': 'Original Information',
                'detail.changedInfo': 'Changed Information',
                'detail.operator': 'Operator',
                'detail.operationTime': 'Operation Time',
                'form.prospectId': 'Prospect ID',
                'data.idCard': 'ID Card',
                'data.following': 'Following',
                'data.kualaLumpur': 'Kuala Lumpur',
                'data.cityCenter': 'City Center',
                'prospects.prospect': 'Prospect',
                'source.superApp': 'Super APP',
                'source.outletCreated': 'Outlet Created'
            }
        };

        // 当前语言
        let currentLanguage = 'zh';

        // 切换语言
        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-switcher .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${lang}`).classList.add('active');
            
            // 更新页面文本
            updatePageText();
            
            // 更新表格数据内容
            updateTableDataText();
            
            // 保存语言偏好
            localStorage.setItem('language', lang);
        }

        // 更新表格数据文本
        function updateTableDataText() {
            const tableBody = document.getElementById('tableBody');
            if (!tableBody) return;
            
            const rows = tableBody.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 2) {
                    // 更新潜客来源列（第三列，索引为2）
                    const sourceCell = cells[2];
                    if (sourceCell) {
                        const sourceText = sourceCell.textContent.trim();
                        if (sourceText === 'Super APP') {
                            sourceCell.textContent = t('source.superApp');
                        } else if (sourceText === 'Outlet Created' || sourceText === '门店创建') {
                            sourceCell.textContent = t('source.outletCreated');
                        }
                    }
                    
                    // 更新潜客状态列（第七列，索引为6）
                    const statusCell = cells[6];
                    if (statusCell) {
                        const statusSpan = statusCell.querySelector('.tag');
                        if (statusSpan) {
                            const statusText = statusSpan.textContent.trim();
                            if (statusText === 'Following' || statusText === '跟进中') {
                                statusSpan.textContent = t('status.following.label');
                            } else if (statusText === 'Lost' || statusText === '无意向') {
                                statusSpan.textContent = t('status.lost.label');
                            } else if (statusText === 'Completed' || statusText === '已成交' || statusText === 'Closed (Won)') {
                                statusSpan.textContent = t('status.completed.label');
                            }
                        }
                    }
                }
            });
        }

        // 获取翻译文本
        function t(key) {
            return i18nResources[currentLanguage][key] || key;
        }

        // 更新页面文本
        function updatePageText() {
            // 更新所有带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                const translation = t(key);
                
                // 特殊处理分页记录文本
                if (key === 'pagination.records') {
                    const totalCount = document.getElementById('totalCount').textContent;
                    if (currentLanguage === 'en') {
                        element.innerHTML = `${translation} <span id="totalCount">${totalCount}</span> records`;
                    } else {
                        element.innerHTML = `${translation} <span id="totalCount">${totalCount}</span> 条记录`;
                    }
                } else {
                    element.textContent = translation;
                }
            });
            
            // 更新所有带有 data-i18n-placeholder 属性的元素
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                element.placeholder = t(key);
            });
            
            // 更新表格中的状态标签
            updateTableStatusLabels();
            
            // 更新所有按钮的 tooltip
            updateButtonTooltips();
            
            // 更新详情模态框中的数据（如果模态框正在显示）
            updateDetailModalData();
        }
        
        // 更新表格状态标签
        function updateTableStatusLabels() {
            document.querySelectorAll('#tableBody tr').forEach(row => {
                const statusCell = row.children[6]; // 状态列（第7列，索引6）
                const statusTag = statusCell.querySelector('.tag');
                if (statusTag) {
                    const statusText = statusTag.textContent.trim();
                    if (statusText === 'Following' || statusText === '跟进中') {
                        statusTag.textContent = t('status.following.label');
                    } else if (statusText === 'Lost' || statusText === '无意向') {
                        statusTag.textContent = t('status.lost.label');
                    } else if (statusText === 'Completed' || statusText === '已成交' || statusText === 'Closed (Won)') {
                        statusTag.textContent = t('status.completed.label');
                    }
                }
            });
        }
        
        // 更新按钮 tooltip
        function updateButtonTooltips() {
            document.querySelectorAll('.btn-tooltip').forEach(btn => {
                const tooltip = btn.getAttribute('data-tooltip');
                if (tooltip === 'Details' || tooltip === '详情') {
                    btn.setAttribute('data-tooltip', t('tooltip.details'));
                } else if (tooltip === 'Follow Up' || tooltip === '跟进') {
                    btn.setAttribute('data-tooltip', t('tooltip.followUp'));
                } else if (tooltip === 'Mark as Lost' || tooltip === '标记无意向') {
                    btn.setAttribute('data-tooltip', t('tooltip.markAsLost'));
                } else if (tooltip === 'Assign SA' || tooltip === '分配顾问') {
                    btn.setAttribute('data-tooltip', t('tooltip.assignSA'));
                }
            });
        }

        // 更新详情模态框中的数据
        function updateDetailModalData() {
            const detailModal = document.getElementById('customerDetailModal');
            if (detailModal && detailModal.classList.contains('show')) {
                const detailStatus = document.getElementById('detailStatus');
                const detailIdType = document.getElementById('detailIdType');
                const detailRegion = document.getElementById('detailRegion');
                const detailAddress = document.getElementById('detailAddress');
                
                if (detailStatus && detailStatus.value) {
                    if (detailStatus.value === '跟进中' || detailStatus.value === 'Following') {
                        detailStatus.value = t('data.following');
                    }
                }
                
                if (detailIdType && detailIdType.value) {
                    if (detailIdType.value === '身份证' || detailIdType.value === 'ID Card') {
                        detailIdType.value = t('data.idCard');
                    }
                }
                
                if (detailRegion && detailRegion.value) {
                    if (detailRegion.value === '吉隆坡' || detailRegion.value === 'Kuala Lumpur') {
                        detailRegion.value = t('data.kualaLumpur');
                    }
                }
                
                if (detailAddress && detailAddress.value) {
                    if (detailAddress.value.includes('市中心') || detailAddress.value.includes('City Center')) {
                        detailAddress.value = t('data.kualaLumpur') + ' ' + t('data.cityCenter');
                    }
                }
            }
        }

        // 生成操作按钮
        function generateActionButtons(id, status) {
            let buttons = '';
            
            // 详情按钮 - 所有状态都显示且可点击
            buttons += `<button class="btn btn-success btn-sm btn-tooltip" data-tooltip="${t('tooltip.details')}" onclick="viewDetail(${id})">
                <i class="bi bi-eye"></i>
            </button> `;
            
            // 跟进按钮 - 所有状态都显示，但根据状态决定是否可点击
            if (status === '跟进中' || status === 'Following') {
                buttons += `<button class="btn btn-primary btn-sm btn-tooltip" data-tooltip="${t('tooltip.followUp')}" onclick="showFollowUpModal(${id})">
                    <i class="bi bi-chat-dots"></i>
                </button> `;
            } else {
                buttons += `<button class="btn btn-secondary btn-sm btn-tooltip disabled" data-tooltip="${t('tooltip.followUp')}" disabled style="opacity: 0.5; cursor: not-allowed;">
                    <i class="bi bi-chat-dots"></i>
                </button> `;
            }
            
            // 标记无意向按钮 - 所有状态都显示，但根据状态决定是否可点击
            if (status === '跟进中' || status === 'Following') {
                buttons += `<button class="btn btn-danger btn-sm btn-tooltip" data-tooltip="${t('tooltip.markAsLost')}" onclick="showDefeatModal(${id})">
                    <i class="bi bi-x-circle"></i>
                </button> `;
            } else {
                buttons += `<button class="btn btn-secondary btn-sm btn-tooltip disabled" data-tooltip="${t('tooltip.markAsLost')}" disabled style="opacity: 0.5; cursor: not-allowed;">
                    <i class="bi bi-x-circle"></i>
                </button> `;
            }
            
            return buttons;
        }

        // 生成潜客ID
        function generateProspectId() {
            const today = new Date();
            const year = today.getFullYear().toString().slice(-2);
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            const random = String(Math.floor(Math.random() * 999) + 1).padStart(3, '0');
            return `P${year}${month}${day}${random}`;
        }

        // 显示新增潜客模态框
        function showAddModal() {
            const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
            
            // 清空表单
            document.getElementById('addCustomerForm').reset();
            document.getElementById('searchCustomerName').value = '';
            document.getElementById('searchCustomerPhone').value = '';
            
            // 生成并设置新的潜客ID
            const newProspectId = generateProspectId();
            document.getElementById('addProspectId').value = newProspectId;
            
            modal.show();
        }

        // 显示跟进模态框
        function showFollowUpModal(id) {
            // 根据ID加载潜客数据
            const modal = new bootstrap.Modal(document.getElementById('followUpModal'));
            
            // 重置表单
            document.getElementById('followUpForm').reset();
            
            // 这里应该加载实际的潜客数据
            document.getElementById('followUpCustomerName').value = `${t('prospects.prospect')}${id}`;
            document.getElementById('followUpCustomerPhone').value = '************';
            document.getElementById('followUpConsultant').value = 'Tan Wei Ming';
            
            // 设置当前时间为默认跟进时间
            const now = new Date();
            const formattedNow = now.getFullYear() + '-' + 
                String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                String(now.getDate()).padStart(2, '0') + 'T' + 
                String(now.getHours()).padStart(2, '0') + ':' + 
                String(now.getMinutes()).padStart(2, '0');
            document.getElementById('followUpTime').value = formattedNow;
            
            // 清空需要重新输入的字段
            document.getElementById('followUpMethod').value = '';
            document.getElementById('intentionLevel').value = '';
            document.getElementById('nextFollowUpTime').value = '';
            document.getElementById('followUpContent').value = '';
                            document.getElementById('followUpTimeTip').textContent = t('tip.selectIntentionFirst');
            
            modal.show();
        }

        // 显示标记无意向模态框
        function showDefeatModal(id) {
            // 根据ID加载潜客数据
            const modal = new bootstrap.Modal(document.getElementById('defeatModal'));
            
            // 这里应该加载实际的潜客数据
            document.getElementById('defeatCustomerName').value = `${t('prospects.prospect')}${id}`;
            document.getElementById('defeatCustomerPhone').value = '************';
            document.getElementById('defeatConsultant').value = 'Tan Wei Ming';
            document.getElementById('defeatApplyTime').value = new Date().toLocaleString();
            
            modal.show();
        }

        // 查看详情
        function viewDetail(id) {
            const modal = new bootstrap.Modal(document.getElementById('customerDetailModal'));
            
            // 从表格中获取对应行的数据
            const tableRows = document.querySelectorAll('#tableBody tr');
            let rowData = null;
            
            for (let row of tableRows) {
                const rowId = row.querySelector('button[onclick*="viewDetail"]').getAttribute('onclick').match(/\d+/)[0];
                if (rowId == id) {
                    rowData = {
                        prospectId: row.children[1].textContent.trim(),
                        source: row.children[2].textContent.trim(),
                        name: row.children[3].textContent.trim(),
                        phone: row.children[4].textContent.trim(),
                        status: row.children[6].textContent.trim()
                    };
                    break;
                }
            }
            
            // 使用实际的潜客数据，确保潜客来源只能是"Super APP"或"门店创建"
            if (rowData) {
                document.getElementById('detailProspectId').value = rowData.prospectId;
                document.getElementById('detailSource').value = rowData.source;
                document.getElementById('detailStatus').value = rowData.status;
                document.getElementById('detailName').value = rowData.name;
                document.getElementById('detailPhone').value = rowData.phone;
            } else {
                // 默认值，确保潜客来源符合要求
                document.getElementById('detailProspectId').value = `P240117${String(id).padStart(3, '0')}`;
                document.getElementById('detailSource').value = 'Super APP';
                document.getElementById('detailStatus').value = t('data.following');
                document.getElementById('detailName').value = `${t('prospects.prospect')}${id}`;
                document.getElementById('detailPhone').value = '************';
            }
            
            // 其他字段保持模拟数据
            document.getElementById('detailIdType').value = t('data.idCard');
            document.getElementById('detailIdNumber').value = '1234567890123456';
            document.getElementById('detailEmail').value = '<EMAIL>';
            document.getElementById('detailRegion').value = t('data.kualaLumpur');
            document.getElementById('detailAddress').value = t('data.kualaLumpur') + ' ' + t('data.cityCenter');
            
            modal.show();
        }

        // 保存跟进记录
        function saveFollowUp() {
            const followUpTime = document.getElementById('followUpTime').value;
            const intentionLevel = document.getElementById('intentionLevel').value;
            const nextFollowUpTime = document.getElementById('nextFollowUpTime').value;
            
            if (!followUpTime || !intentionLevel || !nextFollowUpTime) {
                alert(t('message.fillCompleteFollowUp'));
                return;
            }
            
            // 这里应该提交数据到后端
            alert(t('message.followUpSaved'));
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('followUpModal'));
            modal.hide();
            
            // 刷新列表
            loadProspects();
        }

        // 提交标记无意向
        function submitDefeatRequest() {
            const defeatReason = document.getElementById('defeatReason').value;
            const defeatDescription = document.getElementById('defeatDescription').value;

            if (!defeatReason || !defeatDescription) {
                alert(t('message.fillCompleteLostInfo'));
                return;
            }

            // 这里应该提交数据到后端
            alert(t('message.markAsLostSubmitted'));

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('defeatModal'));
            modal.hide();

            // 刷新列表
            loadProspects();
        }

        // 显示分配顾问模态框
        function showAssignSAModal(id) {
            const modal = new bootstrap.Modal(document.getElementById('assignSAModal'));

            // 获取当前行数据
            const tableBody = document.getElementById('tableBody');
            const rows = tableBody.querySelectorAll('tr');
            let rowData = null;

            for (let row of rows) {
                const seqCell = row.querySelector('.col-seq');
                if (seqCell && seqCell.textContent.trim() === id.toString()) {
                    rowData = {
                        prospectName: row.children[3].textContent.trim(),
                        prospectPhone: row.children[4].textContent.trim(),
                        currentSA: row.children[11].textContent.trim() // SA Name列
                    };
                    break;
                }
            }

            // 填充表单数据
            if (rowData) {
                document.getElementById('assignProspectName').value = rowData.prospectName;
                document.getElementById('assignProspectPhone').value = rowData.prospectPhone;
                document.getElementById('currentSA').value = rowData.currentSA;
            } else {
                // 默认值
                document.getElementById('assignProspectName').value = `Prospect ${id}`;
                document.getElementById('assignProspectPhone').value = '************';
                document.getElementById('currentSA').value = 'Tan Wei Ming';
            }

            // 重置选择和原因
            document.getElementById('newSA').value = '';
            document.getElementById('assignmentReason').value = '';

            modal.show();
        }

        // 保存分配
        function saveAssignment() {
            const newSA = document.getElementById('newSA').value;
            const assignmentReason = document.getElementById('assignmentReason').value;

            if (!newSA) {
                alert('Please select a SA to assign to.');
                return;
            }

            // 这里应该提交数据到后端
            alert('SA assignment saved successfully!');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('assignSAModal'));
            modal.hide();

            // 刷新列表
            loadProspects();
        }

        // 重置筛选表单
        function resetForm() {
            document.getElementById('filterForm').reset();
            loadProspects();
        }

        // 搜索现有客户
        function searchExistingCustomer() {
            const name = document.getElementById('searchCustomerName').value;
            const phone = document.getElementById('searchCustomerPhone').value;
            
            if (!name && !phone) {
                alert(t('message.enterNameOrPhone'));
                return;
            }
            
            // 这里应该调用后端API搜索客户
            console.log('搜索客户:', name, phone);
            alert(t('message.searchRequiresAPI'));
        }

        // 保存客户
        function saveCustomer() {
            const form = document.getElementById('addCustomerForm');
            const formData = new FormData(form);
            
            // 基本表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // 这里应该提交数据到后端
            alert(t('message.customerCreated'));
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
            modal.hide();
            
            // 刷新列表
            loadProspects();
        }

        // 导出数据
        function exportData() {
            // 这里应该调用后端API导出数据
            console.log('导出数据...');
            alert(t('message.exportRequiresAPI'));
        }

        // 按日期筛选
        function filterByDate(type) {
            // 更新tab按钮状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 根据类型设置正确的按钮为active
            if (type === 'today') {
                document.querySelector('.tab-button[onclick="filterByDate(\'today\')"]').classList.add('active');
            } else {
                document.querySelector('.tab-button[onclick="filterByDate(\'all\')"]').classList.add('active');
            }
            
            const tableBody = document.getElementById('tableBody');
            const rows = Array.from(tableBody.querySelectorAll('tr'));
            
            // 获取今天的日期
            const today = new Date();
            const todayStr = today.getFullYear() + '-' + 
                String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                String(today.getDate()).padStart(2, '0');
            
            // 为了演示效果，使用固定日期 2024-01-17
            const demoTodayStr = '2024-01-17';
            
            rows.forEach(row => {
                const nextFollowUpTime = row.getAttribute('data-next-followup');
                
                let shouldShow = true;
                
                if (type === 'today') {
                    // 今日筛选：显示下次跟进时间为今天的潜客
                    if (nextFollowUpTime === '-' || !nextFollowUpTime.startsWith(demoTodayStr)) {
                        shouldShow = false;
                    }
                } else {
                    // 全部筛选：显示所有潜客
                    shouldShow = true;
                }
                
                // 显示或隐藏行
                row.style.display = shouldShow ? '' : 'none';
            });
            
            // 重新计算显示行的序号
            let visibleIndex = 1;
            rows.forEach(row => {
                if (row.style.display !== 'none') {
                    const seqCell = row.querySelector('.col-seq');
                    if (seqCell) {
                        seqCell.textContent = visibleIndex;
                        visibleIndex++;
                    }
                }
            });
            
            // 更新总数显示
            const visibleCount = rows.filter(row => row.style.display !== 'none').length;
            document.getElementById('totalCount').textContent = visibleCount;
            
            // 筛选后重新应用语言翻译
            updateTableDataText();
            
            // 显示筛选状态提示
            if (type === 'today') {
                console.log('筛选类型: 今日需要跟进的潜客', '显示数量:', visibleCount);
                if (visibleCount === 0) {
                    console.log('今日暂无需要跟进的潜客');
                } else {
                    console.log('今日有 ' + visibleCount + ' 个潜客需要跟进');
                }
            } else {
                console.log('筛选类型: 全部潜客', '显示数量:', visibleCount);
            }
        }

        // 排序表格
        function sortTable(column) {
            const sortIcon = document.getElementById('sort-' + column);
            const tableBody = document.getElementById('tableBody');
            const rows = Array.from(tableBody.querySelectorAll('tr'));
            
            // 确定排序方向
            let isAscending = true;
            if (sortIcon.classList.contains('bi-arrow-down-up')) {
                sortIcon.className = 'bi bi-arrow-up sort-icon active';
                isAscending = true;
            } else if (sortIcon.classList.contains('bi-arrow-up')) {
                sortIcon.className = 'bi bi-arrow-down sort-icon active';
                isAscending = false;
            } else {
                sortIcon.className = 'bi bi-arrow-down-up sort-icon';
                return; // 恢复原始状态，不排序
            }
            
            // 重置其他排序图标
            document.querySelectorAll('.sort-icon').forEach(icon => {
                if (icon !== sortIcon) {
                    icon.className = 'bi bi-arrow-down-up sort-icon';
                }
            });
            
            // 执行排序
            rows.sort((a, b) => {
                let aValue, bValue;
                
                if (column === 'level') {
                    // 意向级别排序：H > A > B > C
                    const levelOrder = { 'H': 4, 'A': 3, 'B': 2, 'C': 1 };
                    aValue = levelOrder[a.getAttribute('data-level')] || 0;
                    bValue = levelOrder[b.getAttribute('data-level')] || 0;
                } else {
                    // 其他列按字符串排序
                    aValue = a.getAttribute('data-' + column) || '';
                    bValue = b.getAttribute('data-' + column) || '';
                }
                
                if (aValue < bValue) return isAscending ? -1 : 1;
                if (aValue > bValue) return isAscending ? 1 : -1;
                return 0;
            });
            
            // 重新排列表格行
            tableBody.innerHTML = '';
            rows.forEach((row, index) => {
                // 更新序号
                const seqCell = row.querySelector('.col-seq');
                if (seqCell) {
                    seqCell.textContent = index + 1;
                }
                tableBody.appendChild(row);
            });
            
            // 排序后重新应用语言翻译
            updateTableDataText();
            
            console.log('排序完成 - 列:', column, '升序:', isAscending);
        }

        // 分页
        function changePage(direction) {
            const currentPage = parseInt(document.getElementById('currentPage').textContent);
            let newPage = currentPage;
            
            if (direction === 'prev' && currentPage > 1) {
                newPage = currentPage - 1;
            } else if (direction === 'next') {
                newPage = currentPage + 1;
            }
            
            if (newPage !== currentPage) {
                document.getElementById('currentPage').textContent = newPage;
                loadProspects();
            }
        }

        // 改变页面大小
        function changePageSize(size) {
            console.log('改变页面大小:', size);
            document.getElementById('currentPage').textContent = '1';
            loadProspects();
        }

        // 加载潜客数据
        function loadProspects() {
            // 这里应该从后端加载数据
            console.log('加载潜客数据...');
            filterProspects();
        }

        // 搜索/筛选潜客
        function filterProspects() {
            const customerName = document.getElementById('customerName').value.toLowerCase().trim();
            const customerPhone = document.getElementById('customerPhone').value.toLowerCase().trim();
            const prospectId = document.getElementById('prospectId').value.toLowerCase().trim();
            const sourceChannel = document.getElementById('sourceChannel').value;
            const customerLevel = document.getElementById('customerLevel').value;
            const customerStatus = document.getElementById('customerStatus').value;
            
            const tableBody = document.getElementById('tableBody');
            const rows = Array.from(tableBody.querySelectorAll('tr'));
            
            let visibleCount = 0;
            
            rows.forEach(row => {
                let shouldShow = true;
                
                // 获取行数据
                const rowProspectId = row.children[1].textContent.toLowerCase().trim();
                const rowSource = row.children[2].textContent.toLowerCase().trim();
                const rowName = row.children[3].textContent.toLowerCase().trim();
                const rowPhone = row.children[4].textContent.toLowerCase().trim();
                const rowLevel = row.getAttribute('data-level');
                const rowStatus = row.getAttribute('data-status');
                
                // 检查各个筛选条件
                if (prospectId && !rowProspectId.includes(prospectId)) {
                    shouldShow = false;
                }
                
                if (customerName && !rowName.includes(customerName)) {
                    shouldShow = false;
                }
                
                if (customerPhone && !rowPhone.includes(customerPhone)) {
                    shouldShow = false;
                }
                
                if (sourceChannel && sourceChannel !== 'all' && sourceChannel !== '') {
                    if (sourceChannel === 'Super APP' && !rowSource.includes('Super APP')) {
                        shouldShow = false;
                    } else if (sourceChannel === 'Outlet Created' && !rowSource.includes('Outlet Created')) {
                        shouldShow = false;
                    }
                }
                
                if (customerLevel && customerLevel !== 'all' && rowLevel !== customerLevel) {
                    shouldShow = false;
                }
                
                if (customerStatus && customerStatus !== 'all' && rowStatus !== customerStatus) {
                    shouldShow = false;
                }
                
                // 显示或隐藏行
                row.style.display = shouldShow ? '' : 'none';
                if (shouldShow) {
                    visibleCount++;
                }
            });
            
            // 重新计算显示行的序号
            let visibleIndex = 1;
            rows.forEach(row => {
                if (row.style.display !== 'none') {
                    const seqCell = row.querySelector('.col-seq');
                    if (seqCell) {
                        seqCell.textContent = visibleIndex;
                        visibleIndex++;
                    }
                }
            });
            
            // 更新总数显示
            document.getElementById('totalCount').textContent = visibleCount;
            
            // 应用语言翻译
            updateTableDataText();
            
            console.log('筛选完成，显示数量:', visibleCount);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化国际化
            const savedLanguage = localStorage.getItem('language') || 'zh';
            switchLanguage(savedLanguage);
            
            loadProspects();
            
            // 默认显示全部潜客
            setTimeout(() => {
                filterByDate('all');
                // 确保表格数据也应用了正确的语言
                updateTableDataText();
            }, 100);
            
            // 初始化tooltip位置
            function initTooltips() {
                document.querySelectorAll('.btn-tooltip').forEach(function(btn) {
                    btn.addEventListener('mouseenter', function() {
                        const rect = btn.getBoundingClientRect();
                        const tooltip = btn;
                        tooltip.style.setProperty('--tooltip-top', rect.top + 'px');
                        tooltip.style.setProperty('--tooltip-left', (rect.left + rect.width / 2) + 'px');
                    });
                });
            }
            
            // 初始化tooltip
            initTooltips();
            
            // 当表格内容更新时重新初始化tooltip
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        initTooltips();
                    }
                });
            });
            
            // 观察表格内容变化
            const tableBody = document.getElementById('tableBody');
            if (tableBody) {
                observer.observe(tableBody, { childList: true, subtree: true });
            }

            // 添加跟进时间自动计算功能
            const intentionLevelSelect = document.getElementById('intentionLevel');
            const followUpTimeInput = document.getElementById('followUpTime');
            const nextFollowUpTimeInput = document.getElementById('nextFollowUpTime');
            const followUpTimeTip = document.getElementById('followUpTimeTip');

            function calculateNextFollowUpTime() {
                const level = intentionLevelSelect.value;
                const followUpTime = followUpTimeInput.value;
                
                if (level && followUpTime) {
                    const followUpDate = new Date(followUpTime);
                    let nextFollowUpDate = new Date(followUpDate);
                    
                    // 根据意向级别计算下次跟进时间
                    switch (level) {
                        case 'H':
                            nextFollowUpDate.setDate(followUpDate.getDate() + 3);
                            followUpTimeTip.textContent = t('tip.highIntention');
                            break;
                        case 'A':
                            nextFollowUpDate.setDate(followUpDate.getDate() + 7);
                            followUpTimeTip.textContent = t('tip.mediumHighIntention');
                            break;
                        case 'B':
                            nextFollowUpDate.setDate(followUpDate.getDate() + 14);
                            followUpTimeTip.textContent = t('tip.mediumLowIntention');
                            break;
                        case 'C':
                            nextFollowUpDate.setDate(followUpDate.getDate() + 30);
                            followUpTimeTip.textContent = t('tip.lowIntention');
                            break;
                        default:
                            followUpTimeTip.textContent = t('tip.selectIntentionFirst');
                            return;
                    }
                    
                    // 格式化日期为 datetime-local 格式
                    const year = nextFollowUpDate.getFullYear();
                    const month = String(nextFollowUpDate.getMonth() + 1).padStart(2, '0');
                    const day = String(nextFollowUpDate.getDate()).padStart(2, '0');
                    const hours = String(nextFollowUpDate.getHours()).padStart(2, '0');
                    const minutes = String(nextFollowUpDate.getMinutes()).padStart(2, '0');
                    
                    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
                    nextFollowUpTimeInput.value = formattedDate;
                } else {
                    followUpTimeTip.textContent = t('tip.selectIntentionFirst');
                }
            }

            // 监听意向级别和跟进时间变化
            if (intentionLevelSelect) {
                intentionLevelSelect.addEventListener('change', calculateNextFollowUpTime);
            }
            if (followUpTimeInput) {
                followUpTimeInput.addEventListener('change', calculateNextFollowUpTime);
            }

            // 添加筛选表单提交事件
            const filterForm = document.getElementById('filterForm');
            if (filterForm) {
                filterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    loadProspects();
                });
            }
        });
    </script>
</body>
</html>