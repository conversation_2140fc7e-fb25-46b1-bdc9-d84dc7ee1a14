### **4.1.1 潜客管理**

 

 

 

| **No.** | **Activity**             | **Documents** | **Description**                                      |
| ------- | ------------------------ | ------------- | ---------------------------------------------------- |
| 1       | 注册/使用Super APP       |               | 客户在Super APP上完成注册或注册后进行操作            |
| 1a      | 车辆预定                 | 销售订单      | 客户通过Super APP进行车辆预定                        |
| 1b      | 同步潜客信息             | 潜客档案      | Super APP将潜客信息同步至DMS厂端系统                 |
| 2       | 是否注册Super APP        |               | 销售顾问判断潜客是否已注册Super APP                  |
| 2a      | 手工创建潜客档案         | 潜客档案      | 销售顾问为未注册APP的客户在DMS中手动创建潜客档案     |
| 2b      | 是否被同门店销售顾问跟进 |               | 系统判断该潜客是否已被本门店的其他销售顾问跟进       |
| 3       | 跟进潜客并更新意向级别   | 潜客档案      | 销售顾问对潜客进行跟进，并根据沟通结果更新其意向级别 |
| 4       | 试驾                     | 试驾单        | 销售顾问安排并记录客户的试驾活动                     |
| 5       | 是否有意向购买？         |               | 销售顾问在跟进后判断客户是否有购买意向               |
| 6       | 创建销售订单             | 销售订单      | 销售顾问为有明确购买意向的客户创建销售订单           |

 

 

### **4.1.2 试驾管理**

 

 

 

 

 

| **No.** | **Activity**     | **Documents** | **Description**                           |
| ------- | ---------------- | ------------- | ----------------------------------------- |
| 1       | 客户是否注册APP? |               | 销售顾问确认客户是否已注册Super APP。     |
| 1a      | 注册APP          |               | 客户在Super APP上完成注册。               |
| 2       | 更新潜客档案     | 潜客档案      | DMS厂端系统根据APP注册信息更新潜客档案。  |
| 3       | 创建试驾单       | 试驾单        | 销售顾问为客户创建试驾单。                |
| 4       | 填写试驾信息     | 试驾单        | 销售顾问在试驾单中记录详细的试驾信息。    |
| 5       | 查询分析试驾数据 |               | DMS厂端系统提供试驾数据的查询和分析功能。 |

 

### **4.1.3 订单管理**

 

 

| **No.** | **Activity**                        | **Documents** | **Description**                                      |
| ------- | ----------------------------------- | ------------- | ---------------------------------------------------- |
| 1a      | 客户下订                            | 销售订单      | 客户通过Super APP提交订单。                          |
| 1b      | 销售顾问创建订单                    | 销售订单      | 销售顾问在DMS系统为客户创建订单。                    |
| 2       | 是否在72小时内支付定金?             |               | 系统检查客户是否在规定时间内支付定金。               |
| 2a      | 取消订单                            | 销售订单      | 系统或销售顾问取消未在规定时间内支付定金的订单。     |
| 3       | 定金支付                            |               | 客户支付订单定金。                                   |
| 4       | 是否需要修改个人信息?               |               | 销售顾问确认客户是否需要修改订单中的个人信息。       |
| 4a      | 提交订单修改请求                    |               | 客户通过Super APP提交订单信息修改请求。              |
| 5       | 是否需要修改车辆颜色?               |               | 销售顾问确认客户是否需要修改车辆颜色。               |
| 5a1     | 订单审核-门店                       | 销售订单      | 门店销售经理审核车辆颜色信息的修改请求。             |
| 6       | 是否取消订单?                       |               | 客户或销售顾问发起订单取消流程。                     |
| 6a1     | 订单审核-门店                       | 销售订单      | 门店销售经理审核订单取消申请。                       |
| 6a3     | 执行退款操作                        |               | 财务人员执行定金退款操作。                           |
| 7       | 支付方式                            |               | 客户选择全款或贷款作为支付方式。                     |
| 8       | 销售顾问帮客户向银行申请贷款 (线下) |               | 销售顾问协助客户准备并提交线下贷款申请。             |
| 9       | 贷款审批是否通过?                   |               | 银行审批客户的贷款申请                               |
| 10      | 申请配车                            |               | 销售顾问为订单提交配车申请                           |
| 11      | 执行配车                            |               | DMS厂端配车专员根据订单和库存情况执行配车            |
| 12      | 更新车辆VIN                         | 销售订单      | 配车成功后，将车辆VIN码更新至对应订单。              |
| 13      | 执行保险投保                        | 保单          | 保险专员通过VIPRO保险系统为车辆投保。                |
| 14      | 支付海关税                          |               | 客户支付车辆所需的海关税。                           |
| 15      | 执行JPJ车辆登记                     |               | 车辆登记专员通过JPJ系统完成车辆的注册登记            |
| 16      | 更新车辆登记信息                    | 销售订单      | 车辆登记完成后，将相关登记信息更新至订单。           |
| 17      | 确认尾款已结清                      |               | 财务人员确认客户已结清所有尾款。                     |
| 18      | 客户确认交车                        | 交车单        | 客户在线下或通过APP确认交车                          |
| 19      | 接收销售订单                        | 销售订单      | ERP系统从DMS接收已创建的销售订单信息。               |
| 20      | 接收财务数据                        |               | ERP系统从DMS接收定金、退款、海关税、尾款等财务数据。 |
| 21      | 接收交车数据                        | 交车单        | ERP系统从DMS接收车辆已交付的数据。                   |

 

### **4.1.4 整车收退款管理**

 

 

 

| **No.** | **Activity**           | **Documents** | **Description**                                              |
| ------- | ---------------------- | ------------- | ------------------------------------------------------------ |
| 1       | 完成付款/退款操作      |               | 客户在Super APP上完成支付或退款。                            |
| 2       | 创建付款/退款信息      |               | DMS店端自动/手动创建收退款信息。                             |
| 3       | 业务类型?              |               | 系统判断当前操作是付款还是退款。                             |
| 3a      | 确认订单详情与审核状态 |               | 在处理退款前，财务人员需要确认关联订单的详细信息和审核状态。 |
| 4       | 确认款项金额与支付方式 |               | 财务人员或销售顾问确认单据的金额和支付方式。                 |
| 5       | 财务信息无误?          |               | 财务人员校验财务信息的准确性。                               |
| 6       | 保存信息               |               | 系统保存最终确认的付款或退款信息。                           |
| 7       | 接收财务数据           |               | ERP系统从DMS接收付款或退款的财务数据。                       |

 

### **4.1.5 配车管理**

 

 

| **No.** | **Activity**     | **Documents** | **Description**                                 |
| ------- | ---------------- | ------------- | ----------------------------------------------- |
| 1       | 生成车辆数据     |               | ERP系统生成车辆的详细数据，包括VIN码等。        |
| 2       | 查看所有车辆资源 |               | DMS厂端系统展示所有可用的车辆资源。             |
| 3       | 查看门店车辆资源 |               | DMS店端系统展示分配给该门店的车辆资源。         |
| 4       | 确认车辆入库     |               | 门店确认车辆已实际到达并入库。                  |
| 4a      | 接收车辆入库信息 |               | ERP系统接收DMS同步的车辆入库信息。              |
| 5       | 提交配车申请     |               | 销售顾问为销售订单提交配车申请。                |
| 6       | 审批是否通过?    |               | DMS厂端配车专员对门店提交的配车申请进行审批。   |
| 7       | 确认配车         | 销售订单      | DMS厂端配车专员确认配车，将车辆分配给指定订单。 |
| 8       | 查看配车信息     | 销售订单      | 门店销售顾问查看订单的配车结果。                |

 

### **4.1.6 车辆保险管理**

 

 

| **No.** | **Activity**         | **Documents** | **Description**                                  |
| ------- | -------------------- | ------------- | ------------------------------------------------ |
| 1       | 客户线下确认保险方案 |               | 销售顾问与客户在线下沟通并确认最终的保险方案。   |
| 2       | 推送订单信息         |               | DMS店端将需要投保的订单信息推送至VIPRO保险系统。 |
| 3       | 是否投保成功？       |               | VIPRO保险系统处理投保请求并返回结果。            |
| 3a      | 生成保单信息         | 保单          | 投保成功后，VIPRO系统生成正式的电子保单。        |
| 3b      | 是否再次投保？       |               | 投保失败后，销售顾问确认是否需要重新投保。       |
| 4       | 更新保险信息         | 销售订单      | DMS店端接收并更新订单关联的保单信息。            |

 

### **4.1.7 车辆登记管理**

 

 

| **No.** | **Activity**              | **Documents** | **Description**                                      |
| ------- | ------------------------- | ------------- | ---------------------------------------------------- |
| 1       | 支付海关税                |               | 客户支付车辆所需的海关税。                           |
| 1a      | 接收财务数据              |               | ERP系统从DMS接收已支付海关税的财务数据。             |
| 2       | 确认车辆登记信息          |               | 销售顾问在DMS系统中确认待提交的车辆登记信息。        |
| 3       | 推送车辆登记信息到JPJ系统 |               | 车辆登记专员将车辆登记信息推送到JPJ车辆登记系统。    |
| 4       | JPJ登记结果?              |               | JPJ系统返回车辆登记的成功或失败结果。                |
| 4a      | 是否再次提交?             |               | 如果登记失败，销售顾问判断是否要修改信息后再次提交。 |
| 5       | 更新车辆登记信息          | 销售订单      | 登记成功后，DMS系统更新订单中的车辆登记状态和信息。  |

 

### **4.1.8 发票管理**

 

 

| **No.** | **Activity** | **Documents** | **Description**                                              |
| ------- | ------------ | ------------- | ------------------------------------------------------------ |
| 1       | 提交交车     | 交车单        | 在车辆交付流程完成后，系统触发开具发票的流程。               |
| 2       | 发票开具完成 | 发票          | 系统或财务人员完成发票的开具。                               |
| 2a      | 接收发票信息 | 发票          | ERP系统从DMS接收已开具的发票信息。                           |
| 3       | 操作类型?    |               | 用户选择需要对发票进行的操作。                               |
| 3a      | 查看发票详情 | 发票          | 用户在DMS系统中查看指定发票的详细信息。                      |
| 3b      | 打印纸质发票 | 纸质发票      | 用户通过DMS系统打印出发票的纸质版本。                        |
| 3c      | 发送电子发票 | 电子发票      | DMS系统与ERP系统集成，用户将电子发票发送给ERP，由ERP将发票发送给客户。 |

 

### **4.1.9 交车管理**

 

 

| **No.** | **Activity**       | **Documents** | **Description**                                              |
| ------- | ------------------ | ------------- | ------------------------------------------------------------ |
| 1       | 提交交车           |               | 销售顾问在DMS中提交车辆交付申请。                            |
| 2       | 是否可交车?        |               | 系统根据订单状态、车辆状态和付款状态判断是否满足交车条件。   |
| 3       | 生成交车单         | 交车单        | 条件满足后，系统自动生成交车单。                             |
| 4       | 订单来源?          |               | 系统判断订单是通过Super APP创建还是线下创建。                |
| 4a      | 推送交车单到APP    | 交车单        | 对于线上订单，销售顾问将交车单信息推送到客户的Super APP。    |
| 4a1     | 客户确认交车       | 交车单        | 客户在Super APP上确认交车相关信息。                          |
| 4b      | 填写交车信息       | 交车单        | 对于线下订单，销售顾问手动录入交车信息到系统中。             |
| 5       | 更新交车状态       | 销售订单      | 客户确认交车后，系统更新订单和车辆的最终状态为"已交车"。     |
| 6       | 付款类型？         |               | 系统根据订单信息判断是全款支付还是贷款支付。                 |
| 7       | 提交交车资料给银行 |               | 对于贷款购车，需将交车完成的相关资料提交给银行，等待银行放款。 |
| 8       | 接收交车和财务数据 |               | ERP系统接收DMS同步的交车确认数据及相关财务结算数据。         |

 