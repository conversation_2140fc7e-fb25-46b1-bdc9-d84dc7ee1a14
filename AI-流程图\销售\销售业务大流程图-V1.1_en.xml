<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="2">
  <diagram name="Sales Business Process_EN" id="sales-business-flow-en">
    <mxGraphModel dx="2445" dy="1105" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2000" pageHeight="3500" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry y="40" width="460" height="2480" as="geometry" />
        </mxCell>
        <mxCell id="start" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="210" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="app_register" value="Customer registers/uses Super APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="170" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="select_config" value="Selects vehicle model, payment method, store, and SA" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="170" y="290" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="submit_order" value="Submits order creation request" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="170" y="540" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pay_deposit" value="Pays deposit (1000RM)" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="170" y="810" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="460" y="40" width="400" height="2480" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-98" value="Prospect Management&lt;br&gt;Prospect info enters DMS-HQ Prospect Pool" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="130" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="DMS-Dealer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="860" y="40" width="560" height="2480" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-8" target="O3EtOu6idhiOg0Ps1SD6-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qMhBTVZlKrjSLmaqBplH-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-8" target="O3EtOu6idhiOg0Ps1SD6-101" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="570" />
              <mxPoint x="400" y="210" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="qMhBTVZlKrjSLmaqBplH-2" value="If no prospect information" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="qMhBTVZlKrjSLmaqBplH-1" vertex="1" connectable="0">
          <mxGeometry x="-0.862" relative="1" as="geometry">
            <mxPoint x="79" y="-170" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-8" value="Create Customer Order" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="120" y="540" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-14" target="O3EtOu6idhiOg0Ps1SD6-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-14" value="Collect Deposit" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="120" y="680" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-15" target="O3EtOu6idhiOg0Ps1SD6-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-15" value="Vehicle Payment &amp;amp; Refund Mgt.&lt;br&gt;Collect Deposit" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="140" y="810" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-21" target="O3EtOu6idhiOg0Ps1SD6-22" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="1050" />
              <mxPoint x="110" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-28" value="Full Payment" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="O3EtOu6idhiOg0Ps1SD6-25" vertex="1" connectable="0">
          <mxGeometry x="-0.1824" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-21" target="O3EtOu6idhiOg0Ps1SD6-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="1050" />
              <mxPoint x="280" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-29" value="Loan" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="O3EtOu6idhiOg0Ps1SD6-27" vertex="1" connectable="0">
          <mxGeometry x="-0.186" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-21" value="Payment Method?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="100" y="930" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-22" target="O3EtOu6idhiOg0Ps1SD6-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="110" y="1250" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-22" value="Pay Partial Final Payment" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="50" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-23" target="O3EtOu6idhiOg0Ps1SD6-44" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-23" value="Submit Loan Application" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="220" y="1090" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-31" target="O3EtOu6idhiOg0Ps1SD6-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-31" value="Test Drive Management&lt;br&gt;Customer In-Store Test Drive" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="140" y="420" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-103" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-2" target="O3EtOu6idhiOg0Ps1SD6-101" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-2" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="160" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-44" target="O3EtOu6idhiOg0Ps1SD6-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-44" value="Vehicle Payment &amp;amp; Refund Mgt.&lt;br&gt;Collect Partial Final Payment" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="220" y="1220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-48" target="O3EtOu6idhiOg0Ps1SD6-50" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-48" value="Vehicle Allocation Mgt.&lt;br&gt;Order Vehicle Allocation" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="220" y="1340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-50" value="Vehicle Insurance Mgt.&lt;br&gt;Pushes insurance application info" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="220" y="1460" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-89" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-56" target="O3EtOu6idhiOg0Ps1SD6-59" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-56" value="Insurance Completed" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="200" y="1570" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-59" value="Vehicle Registration Mgt.&lt;br&gt;Pushes vehicle registration info" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="205" y="1690" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-69" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-64" target="O3EtOu6idhiOg0Ps1SD6-67" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-64" value="Vehicle Registration Completed" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="205" y="1810" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-67" target="O3EtOu6idhiOg0Ps1SD6-71" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-67" target="O3EtOu6idhiOg0Ps1SD6-70" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-67" value="Vehicle Payment &amp;amp; Refund Mgt.&lt;br&gt;Confirms final payment is cleared" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="225" y="1920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-70" target="O3EtOu6idhiOg0Ps1SD6-77" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-70" value="Invoice Management&lt;br&gt;Generate Invoice" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="105" y="2040" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-77" target="O3EtOu6idhiOg0Ps1SD6-75" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-71" value="Vehicle Delivery Mgt.&lt;br&gt;Generate Delivery Order" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="340" y="2040" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-85" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-75" target="O3EtOu6idhiOg0Ps1SD6-83" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-75" value="Vehicle Delivery Mgt.&lt;br&gt;Complete Delivery" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="340" y="2260" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-77" value="Customer inspects vehicle at store" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="320" y="2150" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-78" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-71" target="O3EtOu6idhiOg0Ps1SD6-77" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1260" y="2140" as="sourcePoint" />
            <mxPoint x="1260" y="2290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-83" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="360" y="2380" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-104" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-101" target="O3EtOu6idhiOg0Ps1SD6-102" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-101" value="Prospect Management&lt;br&gt;Create Prospect Profile" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="120" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-106" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane3" source="O3EtOu6idhiOg0Ps1SD6-102" target="O3EtOu6idhiOg0Ps1SD6-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-102" value="Prospect Management&lt;br&gt;SA views prospect list" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="120" y="290" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane5" value="JPJ System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="1720" y="40" width="300" height="2480" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-62" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane5" source="O3EtOu6idhiOg0Ps1SD6-60" target="O3EtOu6idhiOg0Ps1SD6-61" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-60" value="Process vehicle registration application" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane5" vertex="1">
          <mxGeometry x="90" y="1690" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-61" value="Generate vehicle registration information" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane5" vertex="1">
          <mxGeometry x="90" y="1810" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="start" target="app_register" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app_register" target="select_config" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="select_config" target="submit_order" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge33" value="Full Payment" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="50" y="2820" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge34" value="Loan" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="50" y="2820" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="submit_order" target="O3EtOu6idhiOg0Ps1SD6-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="pay_deposit" target="O3EtOu6idhiOg0Ps1SD6-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="swimlane4" value="Insurance System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="1420" y="40" width="300" height="2480" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane4" source="O3EtOu6idhiOg0Ps1SD6-52" target="O3EtOu6idhiOg0Ps1SD6-54" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-52" value="Process insurance application" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane4" vertex="1">
          <mxGeometry x="80" y="1460" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-54" value="Generate policy information" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane4" vertex="1">
          <mxGeometry x="80" y="1570" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-50" target="O3EtOu6idhiOg0Ps1SD6-52" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-54" target="O3EtOu6idhiOg0Ps1SD6-56" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-58" value="Send back insurance policy info" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="O3EtOu6idhiOg0Ps1SD6-57" vertex="1" connectable="0">
          <mxGeometry x="-0.0455" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-59" target="O3EtOu6idhiOg0Ps1SD6-60" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-65" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-61" target="O3EtOu6idhiOg0Ps1SD6-64" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-66" value="Send back vehicle registration info" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="O3EtOu6idhiOg0Ps1SD6-65" vertex="1" connectable="0">
          <mxGeometry x="0.7486" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-79" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="2020" y="40" width="300" height="2480" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-86" value="Sync delivery info to ERP" style="rounded=1;whiteSpace=wrap;html=1;" parent="O3EtOu6idhiOg0Ps1SD6-79" vertex="1">
          <mxGeometry x="80" y="2260" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-91" value="Sync financial info to ERP" style="rounded=1;whiteSpace=wrap;html=1;" parent="O3EtOu6idhiOg0Ps1SD6-79" vertex="1">
          <mxGeometry x="70" y="1920" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-94" value="Sync financial info to ERP" style="rounded=1;whiteSpace=wrap;html=1;" parent="O3EtOu6idhiOg0Ps1SD6-79" vertex="1">
          <mxGeometry x="80" y="1220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-96" value="Sync financial info to ERP" style="rounded=1;whiteSpace=wrap;html=1;" parent="O3EtOu6idhiOg0Ps1SD6-79" vertex="1">
          <mxGeometry x="80" y="810" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oIB11RczhqGJUCfydow--1" value="Sync order info to ERP" style="rounded=1;whiteSpace=wrap;html=1;" parent="O3EtOu6idhiOg0Ps1SD6-79" vertex="1">
          <mxGeometry x="80" y="540" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-75" target="O3EtOu6idhiOg0Ps1SD6-86" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-92" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-67" target="O3EtOu6idhiOg0Ps1SD6-91" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-95" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-44" target="O3EtOu6idhiOg0Ps1SD6-94" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-15" target="O3EtOu6idhiOg0Ps1SD6-96" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O3EtOu6idhiOg0Ps1SD6-99" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="app_register" target="O3EtOu6idhiOg0Ps1SD6-98" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qMhBTVZlKrjSLmaqBplH-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-98" target="O3EtOu6idhiOg0Ps1SD6-101" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qMhBTVZlKrjSLmaqBplH-4" value="Query and sync prospect data to the dealer" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="qMhBTVZlKrjSLmaqBplH-3" vertex="1" connectable="0">
          <mxGeometry x="-0.4235" y="-5" relative="1" as="geometry">
            <mxPoint x="24" y="-5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qMhBTVZlKrjSLmaqBplH-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;entryDx=0;entryDy=0;entryX=0.5;entryY=0;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-14" target="pay_deposit" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oIB11RczhqGJUCfydow--2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="O3EtOu6idhiOg0Ps1SD6-8" target="oIB11RczhqGJUCfydow--1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="e8nPdIJJGgeQGErXTGew" name="第 2 页">
    <mxGraphModel dx="4483" dy="2027" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="xSI4qr48HjltsXX29ekN-1" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="16" y="10" width="460" height="1415" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-2" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-1" vertex="1">
          <mxGeometry x="210" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-3" value="Register the Super APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-1" vertex="1">
          <mxGeometry x="170" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-4" value="Book the vehicle" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-1" vertex="1">
          <mxGeometry x="170" y="290" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-7" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="476" y="10" width="400" height="1415" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-91" value="Update prospect info" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-7" vertex="1">
          <mxGeometry x="130" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-17" value="Execute&amp;nbsp; vehicle allocation" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-7">
          <mxGeometry x="140" y="772" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-9" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="876" y="10" width="560" height="1415" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="xSI4qr48HjltsXX29ekN-13" target="ePy18QtBGYXjSOBWa6wE-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-13" value="Create customer order" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-9" vertex="1">
          <mxGeometry x="120" y="420" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="xSI4qr48HjltsXX29ekN-9" source="xSI4qr48HjltsXX29ekN-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="200" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-30" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-9" vertex="1">
          <mxGeometry x="160" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-52" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-9" vertex="1">
          <mxGeometry x="160" y="1301" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="xSI4qr48HjltsXX29ekN-9" source="xSI4qr48HjltsXX29ekN-92" target="xSI4qr48HjltsXX29ekN-93" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-92" value="Create/query prospect info" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-9" vertex="1">
          <mxGeometry x="120" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="xSI4qr48HjltsXX29ekN-9" source="xSI4qr48HjltsXX29ekN-93" target="xSI4qr48HjltsXX29ekN-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-93" value="Test Drive process" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-9" vertex="1">
          <mxGeometry x="120" y="303" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="ePy18QtBGYXjSOBWa6wE-2" target="ePy18QtBGYXjSOBWa6wE-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-10" value="Loan" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="ePy18QtBGYXjSOBWa6wE-5">
          <mxGeometry x="0.0341" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="ePy18QtBGYXjSOBWa6wE-2" target="ePy18QtBGYXjSOBWa6wE-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-9" value="Cash" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="ePy18QtBGYXjSOBWa6wE-8">
          <mxGeometry x="-0.2827" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-2" value="Payment method?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="100" y="539" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="ePy18QtBGYXjSOBWa6wE-4" target="ePy18QtBGYXjSOBWa6wE-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-4" value="Loan approved" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="242" y="683" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-6" value="Apply for vehicle allocation" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="140" y="772" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-11" value="Pay customs duties" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="140" y="973" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-15" value="Update VIN" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="140" y="872" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="ePy18QtBGYXjSOBWa6wE-6" target="ePy18QtBGYXjSOBWa6wE-15">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1076" y="842" as="sourcePoint" />
            <mxPoint x="1076" y="983" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="ePy18QtBGYXjSOBWa6wE-26" target="ePy18QtBGYXjSOBWa6wE-30">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-26" value="Confirm payment settled" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="140" y="1085" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="xSI4qr48HjltsXX29ekN-9" source="ePy18QtBGYXjSOBWa6wE-30" target="xSI4qr48HjltsXX29ekN-52">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-30" value="Confirm delivery" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-9">
          <mxGeometry x="140" y="1193" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-57" value="JPJ System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="1736" y="10" width="300" height="1415" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-24" value="Execute JPJ registration" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-57">
          <mxGeometry x="91" y="973" width="118" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-61" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="xSI4qr48HjltsXX29ekN-2" target="xSI4qr48HjltsXX29ekN-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-62" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="xSI4qr48HjltsXX29ekN-3" target="xSI4qr48HjltsXX29ekN-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-66" value="VIPRO System（Insurance）" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="1436" y="10" width="300" height="1415" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-19" value="Execute insurance purchase" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-66">
          <mxGeometry x="93" y="872" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-76" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="2036" y="10" width="300" height="1415" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-78" value="Receive delivery info" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-76" vertex="1">
          <mxGeometry x="76" y="1193" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-81" value="Receive order&amp;amp;financial info" style="rounded=1;whiteSpace=wrap;html=1;" parent="xSI4qr48HjltsXX29ekN-76" vertex="1">
          <mxGeometry x="70" y="420" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-28" value="Receive financial info" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="xSI4qr48HjltsXX29ekN-76">
          <mxGeometry x="73" y="1085" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-86" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="xSI4qr48HjltsXX29ekN-3" target="xSI4qr48HjltsXX29ekN-91" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="606" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-90" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="xSI4qr48HjltsXX29ekN-13" target="xSI4qr48HjltsXX29ekN-81" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="xSI4qr48HjltsXX29ekN-91" target="xSI4qr48HjltsXX29ekN-92" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xSI4qr48HjltsXX29ekN-101" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="xSI4qr48HjltsXX29ekN-4" target="xSI4qr48HjltsXX29ekN-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-6" target="ePy18QtBGYXjSOBWa6wE-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-17" target="ePy18QtBGYXjSOBWa6wE-15">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="676" y="912" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-15" target="ePy18QtBGYXjSOBWa6wE-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-19" target="ePy18QtBGYXjSOBWa6wE-11">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1589" y="957" />
              <mxPoint x="1076" y="957" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-11" target="ePy18QtBGYXjSOBWa6wE-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-24" target="ePy18QtBGYXjSOBWa6wE-26">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1886" y="1066" />
              <mxPoint x="1076" y="1066" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-26" target="ePy18QtBGYXjSOBWa6wE-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ePy18QtBGYXjSOBWa6wE-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="ePy18QtBGYXjSOBWa6wE-30" target="xSI4qr48HjltsXX29ekN-78">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
