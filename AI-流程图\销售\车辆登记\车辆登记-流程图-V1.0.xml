<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="3">
  <diagram name="车辆登记流程图" id="vehicle-registration-flow">
    <mxGraphModel dx="1665" dy="756" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="450" y="45" width="500" height="1155" as="geometry" />
        </mxCell>
        <mxCell id="start3" value="开始" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="105" y="55" width="70" height="45" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="start3" target="3FOJIjahfTpmjDUD5Z8M-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="185" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-1" target="create1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;1.前置条件：车辆已投保&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="145" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="create1" target="view1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="create1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;2.系统创建车辆待登记记录&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="255" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="view1" target="push2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="view1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;3.车辆登记人员查看车辆登记管理列表，校对&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;车辆登记信息&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="365" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane1" source="check4" target="jpj_success1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-57" value="成功" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-56" vertex="1" connectable="0">
          <mxGeometry x="-0.2396" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="check4" target="jpj_fail2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-2" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;失败&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-73" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-72" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="jpj_fail2" target="3FOJIjahfTpmjDUD5Z8M-69" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="jpj_success1" target="3FOJIjahfTpmjDUD5Z8M-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-81" target="end1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-69" target="push2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="460" y="765" />
              <mxPoint x="460" y="505" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-75" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-74" vertex="1" connectable="0">
          <mxGeometry x="-0.9312" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-82" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-62" target="3FOJIjahfTpmjDUD5Z8M-81" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="590" y="1480" as="sourcePoint" />
            <mxPoint x="590" y="1700" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="jpj2" target="check4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-61" value="回传登记结果" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-60" vertex="1" connectable="0">
          <mxGeometry x="-0.5899" y="-1" relative="1" as="geometry">
            <mxPoint x="23" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="push2" target="jpj1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="push2" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4.车辆登记人员推送车辆信息至JPJ系统&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="475" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jpj1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4.接收登记请求&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="-290" y="475" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="jpj1" target="jpj2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jpj2" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;5.处理车辆登记&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="-290" y="595" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check4" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6.JPJ登记处理结果？&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="80" y="585" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jpj_fail2" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6b.显示失败结果及原因&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="270" y="595" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-69" target="end1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="340" y="1108" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-9" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;否&lt;/font&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Jutxe842SrzlXbRPoJP3-8" vertex="1" connectable="0">
          <mxGeometry x="-0.8417" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-69" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6b1.是否重新推送？&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="280" y="725" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jpj_success1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6a.生成登记证书号&lt;/font&gt;&lt;/font&gt;&lt;br&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;及登记费用&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="735" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-62" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;7.订单管理&lt;/font&gt;&lt;/font&gt;&lt;br&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;更新订单OTR费用结构&lt;/font&gt;&lt;/font&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="60" y="845" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-81" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;8.JPJ注册状态为&quot;已完成&quot;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="965" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;结束&lt;/font&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="100" y="1085" width="80" height="45" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;政府车辆注册系统（JPJ）&lt;/font&gt;&lt;/font&gt;" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="10" y="45" width="440" height="1155" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="车辆登记流程图_en" id="vehicle-registration-flow_en">
    <mxGraphModel dx="1665" dy="756" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="450" y="45" width="500" height="1155" as="geometry" />
        </mxCell>
        <mxCell id="start3" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="105" y="55" width="70" height="45" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="start3" target="3FOJIjahfTpmjDUD5Z8M-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="185" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-1" target="create1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-1" value="1. Prerequisite: Vehicle is insured" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="145" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="create1" target="view1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="create1" value="2. System creates a &#39;Vehicle Awaiting Registration&#39; record" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="255" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="view1" target="push2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="view1" value="3. Registration staff verifies the vehicle registration information" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="365" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane1" source="check4" target="jpj_success1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-57" value="Success" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-56" vertex="1" connectable="0">
          <mxGeometry x="-0.2396" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="check4" target="jpj_fail2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-2" value="Failed" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-73" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-72" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="jpj_fail2" target="3FOJIjahfTpmjDUD5Z8M-69" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane1" source="jpj_success1" target="3FOJIjahfTpmjDUD5Z8M-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-81" target="end1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-69" target="push2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="460" y="765" />
              <mxPoint x="460" y="505" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-75" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-74" vertex="1" connectable="0">
          <mxGeometry x="-0.9312" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-82" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-62" target="3FOJIjahfTpmjDUD5Z8M-81" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="590" y="1480" as="sourcePoint" />
            <mxPoint x="590" y="1700" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="jpj2" target="check4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-61" value="Sends back registration result" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3FOJIjahfTpmjDUD5Z8M-60" vertex="1" connectable="0">
          <mxGeometry x="-0.5899" y="-1" relative="1" as="geometry">
            <mxPoint x="63" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="push2" target="jpj1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="push2" value="4. Push vehicle information to JPJ System" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="475" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jpj1" value="4. Receives registration request" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="-290" y="475" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane1" source="jpj1" target="jpj2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="jpj2" value="5. Processes vehicle registration" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="-290" y="595" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check4" value="6. JPJ registration result?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="80" y="585" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jpj_fail2" value="6b. Displays failure result and reason" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="270" y="595" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane1" source="3FOJIjahfTpmjDUD5Z8M-69" target="end1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="340" y="1108" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Jutxe842SrzlXbRPoJP3-9" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Jutxe842SrzlXbRPoJP3-8" vertex="1" connectable="0">
          <mxGeometry x="-0.8417" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-69" value="6b1. Resubmit?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="280" y="725" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="jpj_success1" value="6a. Update Registration Certificate No. and registration fees" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="735" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-62" value="7. Order Management&lt;br&gt;Update order OTR fee structure" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="60" y="845" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3FOJIjahfTpmjDUD5Z8M-81" value="8. JPJ registration status is &quot;Completed&quot;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="70" y="965" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end1" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="100" y="1085" width="80" height="45" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="JPJ Registration System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="10" y="45" width="440" height="1155" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="hy5lgUij2zDEF2w8-5hn" name="第 3 页">
    <mxGraphModel dx="868" dy="392" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-1" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="312" y="10" width="289" height="767" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" source="5ISscwhbNmPTw8Z5-HTD-1" target="Hv_I1Tgr5Ks60x7k3VYv-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" source="Hv_I1Tgr5Ks60x7k3VYv-9" target="Hv_I1Tgr5Ks60x7k3VYv-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" source="Hv_I1Tgr5Ks60x7k3VYv-32" target="Hv_I1Tgr5Ks60x7k3VYv-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" source="Hv_I1Tgr5Ks60x7k3VYv-43" target="Hv_I1Tgr5Ks60x7k3VYv-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-49" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Hv_I1Tgr5Ks60x7k3VYv-48" vertex="1" connectable="0">
          <mxGeometry x="-0.28" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" source="Hv_I1Tgr5Ks60x7k3VYv-43" target="Hv_I1Tgr5Ks60x7k3VYv-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-52" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Hv_I1Tgr5Ks60x7k3VYv-51" vertex="1" connectable="0">
          <mxGeometry x="-0.6694" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-2" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" vertex="1">
          <mxGeometry x="109" y="58" width="70" height="45" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-9" value="2.Verifie the vehicle registration info" style="rounded=1;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" vertex="1">
          <mxGeometry x="74" y="219" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-23" value="3. Push vehicle info to JPJ System" style="rounded=1;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" vertex="1">
          <mxGeometry x="74" y="337" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-43" value="4a. Resubmit?" style="rhombus;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" vertex="1">
          <mxGeometry x="84" y="452" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-32" value="5. Update Registration info" style="rounded=1;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" vertex="1">
          <mxGeometry x="74" y="587" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-35" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-1" vertex="1">
          <mxGeometry x="104" y="699" width="80" height="45" as="geometry" />
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-1" value="1.Pay customs duties" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="Hv_I1Tgr5Ks60x7k3VYv-1">
          <mxGeometry x="74" y="122" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="Hv_I1Tgr5Ks60x7k3VYv-1" source="Hv_I1Tgr5Ks60x7k3VYv-2" target="5ISscwhbNmPTw8Z5-HTD-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="604" y="123" as="sourcePoint" />
            <mxPoint x="604" y="239" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-28" value="&lt;font style=&quot;color: rgb(255, 0, 0);&quot;&gt;&lt;b style=&quot;&quot;&gt;*&amp;nbsp;&lt;span style=&quot;font-family: Inter, -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, &amp;quot;SF Pro SC&amp;quot;, &amp;quot;SF Pro Display&amp;quot;, &amp;quot;SF Pro Icons&amp;quot;, &amp;quot;PingFang SC&amp;quot;, &amp;quot;Hiragino Sans GB&amp;quot;, &amp;quot;Microsoft YaHei&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Arial, sans-serif; font-size: 15px; text-align: left; text-wrap-mode: wrap;&quot;&gt;Corrected&lt;/span&gt;&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="Hv_I1Tgr5Ks60x7k3VYv-1">
          <mxGeometry x="-27" y="137" width="95" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-36" value="JPJ Registration System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="11" y="10" width="301" height="767" as="geometry" />
        </mxCell>
        <mxCell id="Hv_I1Tgr5Ks60x7k3VYv-41" value="4. JPJ registration result?" style="rhombus;whiteSpace=wrap;html=1;" parent="Hv_I1Tgr5Ks60x7k3VYv-36" vertex="1">
          <mxGeometry x="100" y="328" width="120" height="78" as="geometry" />
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-5" value="ERP" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="601" y="10" width="289" height="767" as="geometry" />
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-26" value="1a.Receive financial info" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="5ISscwhbNmPTw8Z5-HTD-5">
          <mxGeometry x="74.5" y="122" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="Hv_I1Tgr5Ks60x7k3VYv-23" target="Hv_I1Tgr5Ks60x7k3VYv-41">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="Hv_I1Tgr5Ks60x7k3VYv-41" target="Hv_I1Tgr5Ks60x7k3VYv-43">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="169" y="502" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-24" value="Failed" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="5ISscwhbNmPTw8Z5-HTD-22">
          <mxGeometry x="-0.7743" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Hv_I1Tgr5Ks60x7k3VYv-41">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="386" y="627" as="targetPoint" />
            <Array as="points">
              <mxPoint x="69" y="376" />
              <mxPoint x="69" y="627" />
              <mxPoint x="386" y="627" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-25" value="Sucessful" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="5ISscwhbNmPTw8Z5-HTD-23">
          <mxGeometry x="-0.6547" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5ISscwhbNmPTw8Z5-HTD-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5ISscwhbNmPTw8Z5-HTD-1" target="5ISscwhbNmPTw8Z5-HTD-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
