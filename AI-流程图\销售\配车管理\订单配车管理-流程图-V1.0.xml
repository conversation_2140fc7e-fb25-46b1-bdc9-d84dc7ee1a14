<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="3">
  <diagram name="订单配车管理业务流程图" id="订单配车管理业务流程图">
    <mxGraphModel dx="1332" dy="1432" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="ERP系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="40" y="-60" width="320" height="1980" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-3a1TtDz9TxLToT-5" value="3.接收门店入库信息" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="swimlane1">
          <mxGeometry x="80" y="270" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="360" y="-60" width="350" height="1980" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="click_allocate" target="input_search" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="click_allocate" value="5.查看待配车订单列表数据" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="390" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="input_search" value="6.输入VIN或工厂订单号查询可配车辆" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="60" y="500" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="query_vehicles" target="verify_status" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="query_vehicles" value="7.配车专员确认配车操作" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="610" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge26" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="input_search" target="query_vehicles" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="verify_status" target="ZI1YNsVRHhoN2R3LKbWW-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-22" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-8" vertex="1" connectable="0">
          <mxGeometry x="-0.3531" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="verify_status" target="input_search" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="280" y="760" />
              <mxPoint x="280" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-24" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-23" vertex="1" connectable="0">
          <mxGeometry x="-0.8129" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="verify_status" value="8.是否配车成功？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="100" y="720" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-6" target="ZI1YNsVRHhoN2R3LKbWW-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-6" value="8a.系统自动创建配车记录" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="850" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-7" target="ZI1YNsVRHhoN2R3LKbWW-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-7" value="8a1.更新订单配车状态为已配车" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="950" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-10" target="ZI1YNsVRHhoN2R3LKbWW-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-10" value="8a2.车辆状态为“已锁定”" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1060" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-27" target="ZI1YNsVRHhoN2R3LKbWW-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-35" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-34" vertex="1" connectable="0">
          <mxGeometry x="-0.4198" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-27" target="ZI1YNsVRHhoN2R3LKbWW-43" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="1200" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-47" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-46" vertex="1" connectable="0">
          <mxGeometry x="-0.8805" y="1" relative="1" as="geometry">
            <mxPoint x="19" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-27" value="8a3.需要取消配车？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="100" y="1160" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-52" target="ZI1YNsVRHhoN2R3LKbWW-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-33" value="8a3a.配车专员确认取消配车操作" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-40" target="ZI1YNsVRHhoN2R3LKbWW-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-45" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-44" vertex="1" connectable="0">
          <mxGeometry x="-0.4124" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-40" value="8a3a5.需要再次配车？" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="95" y="1750" width="130" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-40" target="click_allocate" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="330" y="1790" />
              <mxPoint x="330" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-72" value="重新配车" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-42" vertex="1" connectable="0">
          <mxGeometry x="-0.9329" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-43" value="配车流程结束" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="115" y="1890" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-49" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-50" target="ZI1YNsVRHhoN2R3LKbWW-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="1400" as="sourcePoint" />
            <mxPoint x="520" y="1530" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-48" value="8a3a2.更新订单配车状态为未配车" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1520" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-33" target="ZI1YNsVRHhoN2R3LKbWW-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="1400" as="sourcePoint" />
            <mxPoint x="520" y="1560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-50" value="8a3a1.系统自动创建取消配车记录" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1410" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-53" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-48" target="ZI1YNsVRHhoN2R3LKbWW-52" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="1620" as="sourcePoint" />
            <mxPoint x="520" y="1860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-52" value="8a3a3.车辆状态为“未锁定”" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1630" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="erp_sync" value="1.车辆数据生成" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="-240" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="erp_sync" target="ZI1YNsVRHhoN2R3LKbWW-58" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-70" value="同步数据" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-61" vertex="1" connectable="0">
          <mxGeometry x="-0.4898" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="_lNwItVVv_PRC_4RCnYo-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-58" target="ZI1YNsVRHhoN2R3LKbWW-60" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-58" value="1a.配车专员查看所有门店&lt;br&gt;车辆资源" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-60" value="1b.销售顾问查看本门店车辆资源" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="430" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="erp_start" value="开始" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="-190" y="50" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="erp_start" target="erp_sync" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="receive_notification" value="9.销售顾问查看已配车数据" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="430" y="1060" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="enter_page" value="4.总部配车专员进入订单配车管理页面" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="270" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-10" target="receive_notification" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-58" target="enter_page" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-64" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="enter_page" target="click_allocate" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="710" y="-60" width="320" height="1980" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-76" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="115" y="1210" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-17" value="2.订单管理&lt;br&gt;订单达到配车条件" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="95" y="340" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="swimlane3" source="FflVI_3a1TtDz9TxLToT-3" target="ZI1YNsVRHhoN2R3LKbWW-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-3" value="1c.确认车辆入库" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="swimlane3">
          <mxGeometry x="80" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_lNwItVVv_PRC_4RCnYo-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="ZI1YNsVRHhoN2R3LKbWW-17" target="enter_page" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="310" />
              <mxPoint x="750" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ZI1YNsVRHhoN2R3LKbWW-60" target="FflVI_3a1TtDz9TxLToT-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="FflVI_3a1TtDz9TxLToT-3" target="FflVI_3a1TtDz9TxLToT-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-12" value="同步数据" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FflVI_3a1TtDz9TxLToT-11">
          <mxGeometry x="-0.8439" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="receive_notification" target="ZI1YNsVRHhoN2R3LKbWW-76">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="Vehicle Allocation Management Business Process Diagram_en" id="Vehicle_Allocation_Management_Business_Process_Diagram_en">
    <mxGraphModel dx="1665" dy="1583" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="40" y="-60" width="320" height="1980" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-5" value="3.Receive Outlet Inbound Info" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane1" vertex="1">
          <mxGeometry x="80" y="270" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="360" y="-60" width="350" height="1980" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="click_allocate" target="input_search" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="click_allocate" value="5.View List of Orders to be Allocated" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="390" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="input_search" value="6.Enter VIN or Factory Order Number to query for allocatable vehicles" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="60" y="500" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="query_vehicles" target="verify_status" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="query_vehicles" value="7.Allocation Specialist Confirms Allocation" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="610" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge26" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="input_search" target="query_vehicles" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="verify_status" target="ZI1YNsVRHhoN2R3LKbWW-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-22" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-8" vertex="1" connectable="0">
          <mxGeometry x="-0.3531" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="verify_status" target="input_search" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="280" y="760" />
              <mxPoint x="280" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-24" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-23" vertex="1" connectable="0">
          <mxGeometry x="-0.8129" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="verify_status" value="8.Allocation Successful?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="100" y="720" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-6" target="ZI1YNsVRHhoN2R3LKbWW-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-6" value="8a.System Automatically Creates Allocation Record" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="850" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-7" target="ZI1YNsVRHhoN2R3LKbWW-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-7" value="8a1.Update Order Allocation Status to &#39;Allocated&#39;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="950" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-10" target="ZI1YNsVRHhoN2R3LKbWW-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-10" value="8a2.Vehicle Status becomes &quot;Locked&quot;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1060" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-27" target="ZI1YNsVRHhoN2R3LKbWW-33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-35" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-34" vertex="1" connectable="0">
          <mxGeometry x="-0.4198" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-27" target="ZI1YNsVRHhoN2R3LKbWW-43" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="1200" />
              <mxPoint x="50" y="1920" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-47" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-46" vertex="1" connectable="0">
          <mxGeometry x="-0.8805" y="1" relative="1" as="geometry">
            <mxPoint x="19" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-27" value="8a3.Cancel Allocation?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="100" y="1160" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-52" target="ZI1YNsVRHhoN2R3LKbWW-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-33" value="8a3a.Allocation Specialist Confirms Cancel Allocation" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-40" target="ZI1YNsVRHhoN2R3LKbWW-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-45" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-44" vertex="1" connectable="0">
          <mxGeometry x="-0.4124" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-40" value="8a3a5.Reallocate?" style="rhombus;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="95" y="1750" width="130" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-40" target="click_allocate" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="330" y="1790" />
              <mxPoint x="330" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-72" value="Reallocate" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-42" vertex="1" connectable="0">
          <mxGeometry x="-0.9329" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-43" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="115" y="1890" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-49" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-50" target="ZI1YNsVRHhoN2R3LKbWW-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="1400" as="sourcePoint" />
            <mxPoint x="520" y="1530" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-48" value="8a3a2.Update Order Allocation Status to &#39;Awaiting Allocated&#39;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1520" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-33" target="ZI1YNsVRHhoN2R3LKbWW-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="1400" as="sourcePoint" />
            <mxPoint x="520" y="1560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-50" value="8a3a1.System Automatically Creates Cancellation Record" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1410" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-53" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-48" target="ZI1YNsVRHhoN2R3LKbWW-52" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="1620" as="sourcePoint" />
            <mxPoint x="520" y="1860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-52" value="8a3a3.Vehicle Status becomes &quot;Unlocked&quot;" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="1630" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="erp_sync" value="1.Vehicle Data Generation" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="-240" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="erp_sync" target="ZI1YNsVRHhoN2R3LKbWW-58" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-70" value="Sync Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ZI1YNsVRHhoN2R3LKbWW-61" vertex="1" connectable="0">
          <mxGeometry x="-0.4898" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="_lNwItVVv_PRC_4RCnYo-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-58" target="ZI1YNsVRHhoN2R3LKbWW-60" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-58" value="1a.Allocation Specialist Views Vehicle&lt;br&gt;Resources of All Outlets" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-60" value="1b.SA Views Vehicle Resources of This Outlet" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="430" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="erp_start" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="-190" y="50" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="erp_start" target="erp_sync" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="receive_notification" value="9.SA Views Allocated Data" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="430" y="1060" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="enter_page" value="4.HQ Allocation Specialist Enters Vehicle Allocation Management Page" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane2" vertex="1">
          <mxGeometry x="80" y="270" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-10" target="receive_notification" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane2" source="ZI1YNsVRHhoN2R3LKbWW-58" target="enter_page" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-64" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="swimlane2" source="enter_page" target="click_allocate" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="710" y="-60" width="320" height="1980" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-76" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="115" y="1210" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZI1YNsVRHhoN2R3LKbWW-17" value="2.Order Management&lt;br&gt;Order Reaches Allocation Conditions" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="82.5" y="350" width="155" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="swimlane3" source="FflVI_3a1TtDz9TxLToT-3" target="ZI1YNsVRHhoN2R3LKbWW-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-3" value="1c.Confirm Vehicle Inbound" style="rounded=1;whiteSpace=wrap;html=1;" parent="swimlane3" vertex="1">
          <mxGeometry x="80" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_lNwItVVv_PRC_4RCnYo-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="ZI1YNsVRHhoN2R3LKbWW-17" target="enter_page" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="310" />
              <mxPoint x="750" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="ZI1YNsVRHhoN2R3LKbWW-60" target="FflVI_3a1TtDz9TxLToT-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="FflVI_3a1TtDz9TxLToT-3" target="FflVI_3a1TtDz9TxLToT-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-12" value="Sync Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="FflVI_3a1TtDz9TxLToT-11" vertex="1" connectable="0">
          <mxGeometry x="-0.8439" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FflVI_3a1TtDz9TxLToT-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="receive_notification" target="ZI1YNsVRHhoN2R3LKbWW-76" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="VbTxdlPh82vo-BlGJJUt" name="第 3 页">
    <mxGraphModel dx="1494" dy="676" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="y877wFirnA_lHzEWoixq-1" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="49" y="30" width="320" height="673" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-2" value="3.Receive Outlet Inbound Info" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-1" vertex="1">
          <mxGeometry x="80" y="233" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-40" value="1.Vehicle Data Generation" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-1" vertex="1">
          <mxGeometry x="80" y="129" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-3" value="DMS-HQ" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="369" y="30" width="350" height="673" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-8" value="4.Confirms Allocation" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-3" vertex="1">
          <mxGeometry x="95" y="454" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-44" value="1a. View Vehicle&lt;br&gt;Resources of All Outlets" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-3" vertex="1">
          <mxGeometry x="95" y="129" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-46" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-3" vertex="1">
          <mxGeometry x="-190" y="50" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-71" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="y877wFirnA_lHzEWoixq-3" source="y877wFirnA_lHzEWoixq-68" target="y877wFirnA_lHzEWoixq-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-73" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="y877wFirnA_lHzEWoixq-71" vertex="1" connectable="0">
          <mxGeometry x="-0.2602" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-68" value="3.Approval?" style="rhombus;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-3" vertex="1">
          <mxGeometry x="109.5" y="336" width="131" height="63" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-53" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;whiteSpace=wrap;html=1;collapsible=0;" parent="1" vertex="1">
          <mxGeometry x="719" y="30" width="320" height="673" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-64" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="y877wFirnA_lHzEWoixq-53" source="y877wFirnA_lHzEWoixq-57" target="y877wFirnA_lHzEWoixq-63" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-57" value="1c.Confirm Vehicle Inbound" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-53" vertex="1">
          <mxGeometry x="80" y="233" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-63" value="2.Submit Allocation" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-53" vertex="1">
          <mxGeometry x="100" y="338" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-48" value="5.View Allocated info" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-53" vertex="1">
          <mxGeometry x="80" y="454" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-62" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="y877wFirnA_lHzEWoixq-53" source="y877wFirnA_lHzEWoixq-48" target="y877wFirnA_lHzEWoixq-54" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-54" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-53" vertex="1">
          <mxGeometry x="115" y="570" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-45" value="1b.View Vehicle Resources of Outlet" style="rounded=1;whiteSpace=wrap;html=1;" parent="y877wFirnA_lHzEWoixq-53" vertex="1">
          <mxGeometry x="80" y="129" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-59" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="y877wFirnA_lHzEWoixq-53" source="y877wFirnA_lHzEWoixq-45" target="y877wFirnA_lHzEWoixq-57" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="M46KZG33t7PiL9m16ccW-1" value="&lt;font style=&quot;color: rgb(255, 0, 0);&quot;&gt;&lt;b style=&quot;&quot;&gt;*&amp;nbsp;&lt;span style=&quot;font-family: Inter, -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, &amp;quot;SF Pro SC&amp;quot;, &amp;quot;SF Pro Display&amp;quot;, &amp;quot;SF Pro Icons&amp;quot;, &amp;quot;PingFang SC&amp;quot;, &amp;quot;Hiragino Sans GB&amp;quot;, &amp;quot;Microsoft YaHei&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, Helvetica, Arial, sans-serif; font-size: 15px; text-align: left; text-wrap-mode: wrap;&quot;&gt;Corrected&lt;/span&gt;&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="y877wFirnA_lHzEWoixq-53">
          <mxGeometry x="220" y="353" width="95" height="30" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="y877wFirnA_lHzEWoixq-57" target="y877wFirnA_lHzEWoixq-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-70" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="y877wFirnA_lHzEWoixq-63" target="y877wFirnA_lHzEWoixq-68" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="879" y="346" />
              <mxPoint x="544" y="346" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="y877wFirnA_lHzEWoixq-68" target="y877wFirnA_lHzEWoixq-63" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-74" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="y877wFirnA_lHzEWoixq-72" vertex="1" connectable="0">
          <mxGeometry x="-0.7132" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="y877wFirnA_lHzEWoixq-8" target="y877wFirnA_lHzEWoixq-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="y877wFirnA_lHzEWoixq-40" target="y877wFirnA_lHzEWoixq-44" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-42" value="Sync Data" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="y877wFirnA_lHzEWoixq-41" vertex="1" connectable="0">
          <mxGeometry x="-0.4898" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-47" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="y877wFirnA_lHzEWoixq-46" target="y877wFirnA_lHzEWoixq-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="y877wFirnA_lHzEWoixq-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="y877wFirnA_lHzEWoixq-44" target="y877wFirnA_lHzEWoixq-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
