<template>
  <div class="part-scrap-form">
    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label for="partName">零件名称</label>
        <input 
          type="text" 
          id="partName" 
          v-model="form.partName" 
          required 
          placeholder="请输入零件名称"
        />
      </div>
      
      <div class="form-group">
        <label for="partNumber">零件编号</label>
        <input 
          type="text" 
          id="partNumber" 
          v-model="form.partNumber" 
          required 
          placeholder="请输入零件编号"
        />
      </div>
      
      <div class="form-group">
        <label for="quantity">报废数量</label>
        <input 
          type="number" 
          id="quantity" 
          v-model="form.quantity" 
          required 
          min="1"
          placeholder="请输入报废数量"
        />
      </div>
      
      <div class="form-group">
        <label for="reason">报废原因</label>
        <select id="reason" v-model="form.reason" required>
          <option value="">请选择报废原因</option>
          <option value="damaged">损坏</option>
          <option value="expired">过期</option>
          <option value="defective">质量缺陷</option>
          <option value="obsolete">淘汰</option>
          <option value="other">其他</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="description">详细描述</label>
        <textarea 
          id="description" 
          v-model="form.description" 
          rows="4" 
          required
          placeholder="请详细描述报废原因和情况"
        ></textarea>
      </div>
      
      <div class="form-group">
        <label for="location">存放位置</label>
        <input 
          type="text" 
          id="location" 
          v-model="form.location" 
          placeholder="请输入零件当前存放位置"
        />
      </div>
      
      <div class="form-actions">
        <button type="button" @click="$emit('close')" class="btn btn-cancel">
          取消
        </button>
        <button type="submit" class="btn btn-submit">
          提交报废申请
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  name: 'PartScrapForm',
  emits: ['close'],
  data() {
    return {
      form: {
        partName: '',
        partNumber: '',
        quantity: 1,
        reason: '',
        description: '',
        location: ''
      }
    }
  },
  methods: {
    submitForm() {
      // 这里应该调用API提交表单
      console.log('提交报废申请:', this.form)
      alert('报废申请提交成功！')
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.part-scrap-form {
  max-width: 500px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.btn-cancel {
  background: #f0f0f0;
  color: #333;
}

.btn-submit {
  background: #1890ff;
  color: white;
}

.btn:hover {
  opacity: 0.9;
}
</style>
