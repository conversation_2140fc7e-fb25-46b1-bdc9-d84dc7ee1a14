@startuml 叫料流程图
!theme plain
skinparam title {
    RoundCorner 15
    BorderThickness 2
    BorderColor #000000
    BackgroundColor #FFFFFF
    FontColor #000000
    FontSize 24
}
skinparam swimlane {
    BorderColor #000000
    BorderThickness 2
    TitleFontColor #000000
    TitleBackgroundColor #FFFFFF
    TitleFontSize 16
}
skinparam activity {
    BorderColor #000000
    BackgroundColor #FFFFFF
    FontColor #000000
    Shadowing false
}
skinparam note {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
}
skinparam arrow {
    Color #000000
    Thickness 2
}

title 叫料流程图

|DMS-店端|
start
:门店操作员创建叫料清单;

|DMS-厂端|
label 审批流程
:HQ审批员审批叫料清单;

if (审批结果?) then (通过)
  |ERP系统|
  :系统接收数据生成采购订单;
  
  |DMS-厂端|
  label 发货检查
  :HQ发货员检查库存情况;
  
  if (库存情况?) then (充足)
    :HQ发货员全部发货;
    
    |DMS-店端|
    :门店操作员全部收货;
    :门店库存增加;
    stop
  else (不足)
    :HQ发货员部分发货;
    note right: 仅发货库存充足的零件
    
    |DMS-店端|
    :门店操作员部分收货;
    :门店库存增加;
    
    |DMS-厂端|
    :系统检查采购订单状态;
    
    if (是否全部发货完成?) then (未完成)
      goto 发货检查
    else (完成)
      stop
    endif
  endif
else (未通过)
  |DMS-店端|
  :门店操作员修改叫料清单;
  note right: 或重新创建新的叫料清单
  :系统重新提交审批;
  goto 审批流程
endif

@enduml
