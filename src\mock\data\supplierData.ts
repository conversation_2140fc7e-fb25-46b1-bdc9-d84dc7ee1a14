// 供应商相关的模拟数据

export const supplierData = {
  suppliers: [
    {
      id: 1,
      name: '博世汽车',
      code: 'BOSCH001',
      contact: '张经理',
      phone: '021-12345678',
      email: '<EMAIL>',
      address: '上海市浦东新区博世路123号',
      status: 'active',
      rating: 5
    },
    {
      id: 2,
      name: '曼牌滤清器',
      code: 'MANN001',
      contact: '李经理',
      phone: '010-87654321',
      email: '<EMAIL>',
      address: '北京市朝阳区曼牌大厦456号',
      status: 'active',
      rating: 4
    },
    {
      id: 3,
      name: 'NGK火花塞',
      code: 'NGK001',
      contact: '王经理',
      phone: '0755-11223344',
      email: '<EMAIL>',
      address: '深圳市南山区NGK工业园789号',
      status: 'active',
      rating: 5
    }
  ],

  // 采购订单数据
  purchaseOrders: [
    {
      id: 1,
      orderNumber: 'PO-2024-001',
      supplierId: 1,
      supplierName: '博世汽车',
      status: 'pending',
      totalAmount: 15000,
      createdAt: '2024-12-25',
      items: [
        {
          partNumber: 'BP-001',
          partName: '刹车片',
          quantity: 50,
          unitPrice: 300,
          totalPrice: 15000
        }
      ]
    }
  ]
}

export default supplierData
