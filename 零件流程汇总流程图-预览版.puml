@startuml 零件流程汇总流程图
!theme plain
skinparam title {
    RoundCorner 15
    BorderThickness 2
    BorderColor #409EFF
    BackgroundColor #ECF5FF
    FontColor #303133
    FontSize 24
}
skinparam swimlane {
    BorderColor #DCDFE6
    BorderThickness 2
    TitleFontColor #303133
    TitleBackgroundColor #ECF5FF
    TitleFontSize 16
}
skinparam activity {
    BorderColor #409EFF
    BackgroundColor #F5F7FA
    FontColor #606266
    Shadowing false
}
skinparam note {
    BackgroundColor #FEFCE8
    BorderColor #F9E2AF
    FontColor #909399
}
skinparam arrow {
    Color #409EFF
    Thickness 2
}

title 零件流程汇总流程图

|DMS-店端|
start
:门店操作员新建叫料清单;

|DMS-厂端|
label 叫料审批
:HQ审批员审批叫料单;

if (审批结果?) then (通过)
  |ERP系统|
  :系统自动生成采购订单;

  |DMS-店端|
  :门店操作员等待收货;
  :门店操作员执行收货;

  |ERP系统|
  :系统接收收货数据;
  :DMS门店库存增加;

  |DMS-店端|
  label 报损流程
  :门店操作员填写报损单;
  :门店操作员提交报损单;

  |DMS-厂端|
  label 报损审批
  :HQ审批员审批报损单;

  if (报损审批结果?) then (通过)
    |ERP系统|
    :系统接收报损信息;

    |DMS-店端|
    :系统生成拣货单;

    label 库存检查
    if (库存是否满足?) then (满足)
      :门店操作员执行拣货;

      if (是否需要退料?) then (需要)
        :门店操作员执行退料;
        :零件库存增加;
        stop
      else (不需要)
        stop
      endif
    else (不满足)
      :拣货单进入缺货状态;
      :等待库存补充;
      goto 库存检查
    endif
  else (未通过)
    |DMS-店端|
    :门店操作员修改报损单;
    :系统重新生成报损单;
    goto 报损审批
  endif
else (未通过)
  |DMS-店端|
  :门店操作员修改叫料单;
  :系统重新生成叫料单;
  goto 叫料审批
endif

@enduml
