<template>
  <div class="home">
    <h1>欢迎使用 DMS 系统</h1>
    <p>这是一个经销商管理系统的前端应用</p>
    
    <div class="feature-cards">
      <div class="card">
        <h3>零件管理</h3>
        <p>管理零件库存、叫料清单等</p>
        <router-link to="/part-management" class="btn">进入</router-link>
      </div>
      
      <div class="card">
        <h3>零件管理HQ</h3>
        <p>总部零件管理功能</p>
        <router-link to="/parts-management-hq" class="btn">进入</router-link>
      </div>
      
      <div class="card">
        <h3>零件收货</h3>
        <p>零件收货确认和处理</p>
        <router-link to="/parts-receipt" class="btn">进入</router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomeView'
}
</script>

<style scoped>
.home {
  text-align: center;
}

h1 {
  color: #333;
  margin-bottom: 1rem;
}

p {
  color: #666;
  margin-bottom: 2rem;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.card:hover {
  transform: translateY(-5px);
}

.card h3 {
  color: #1890ff;
  margin-bottom: 1rem;
}

.card p {
  margin-bottom: 1.5rem;
}

.btn {
  display: inline-block;
  background: #1890ff;
  color: white;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.btn:hover {
  background: #40a9ff;
}
</style>
