<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="Parts Requisition Flowchart-V2.0-Black&amp;White" id="material-request-flow-v20-bw-en">
    <mxGraphModel dx="823" dy="462" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="30" y="37" width="350" height="1363" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="DMS-HQ Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="380" y="37" width="389" height="1363" as="geometry" />
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-3" value="" style="endArrow=classic;html=1;rounded=0;" parent="swimlane2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="89" y="483" as="sourcePoint" />
            <mxPoint x="79" y="603" as="targetPoint" />
            <Array as="points">
              <mxPoint x="79" y="483" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-4" value="Sufficient" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P6dzXHE8lVgDXhEl2eH6-3" vertex="1" connectable="0">
          <mxGeometry x="-0.1856" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="swimlane3" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="769" y="37" width="350" height="1363" as="geometry" />
        </mxCell>
        <mxCell id="start1" value="1. Create Parts Requisition" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="100" y="100" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval1" value="3a. Approve Requisition" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="500" y="214" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision1" value="3b. Approval Result?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="480" y="309" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="generate_po" value="4.&amp;nbsp; Receive Data and Generate Transfer Order" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="826" y="415" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision2" value="5. Check Inventory Status?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="471" y="480" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="full_ship" value="6a. Full Shipment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="396" y="642" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="full_receive" value="7a. Receive Goods" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="85" y="643" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="full_inventory" value="8a. Inventory Increase" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="86" y="747" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end1" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="87" y="856" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="partial_ship" value="6b. Partial Shipment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="484" y="960" width="140" height="61" as="geometry" />
        </mxCell>
        <mxCell id="modify_request" value="3c. Outlet Modify/Redo Requisition" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="191" y="320" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="approval1" target="decision1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="Approved" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;fontColor=#000000;" parent="1" source="decision1" target="generate_po" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="full_ship" target="full_receive" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="full_receive" target="full_inventory" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="full_inventory" target="end1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-9" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="decision1" target="modify_request" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="380" as="sourcePoint" />
            <mxPoint x="360" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-10" value="Rejected" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P6dzXHE8lVgDXhEl2eH6-9" vertex="1" connectable="0">
          <mxGeometry x="-0.497" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-12" value="7b. Partial Receive" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="160" y="960" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="partial_ship" target="P6dzXHE8lVgDXhEl2eH6-12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="820" as="sourcePoint" />
            <mxPoint x="440" y="770" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-15" value="9b1. Is Full Shipment Complete?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="470" y="1084" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="partial_ship" target="P6dzXHE8lVgDXhEl2eH6-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="1120" as="sourcePoint" />
            <mxPoint x="450" y="1070" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-17" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="P6dzXHE8lVgDXhEl2eH6-15" target="P6dzXHE8lVgDXhEl2eH6-19" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="1050" as="sourcePoint" />
            <mxPoint x="310" y="1220" as="targetPoint" />
            <Array as="points">
              <mxPoint x="554" y="1279" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-24" value="&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P6dzXHE8lVgDXhEl2eH6-17" vertex="1" connectable="0">
          <mxGeometry x="-0.7122" y="4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-25" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P6dzXHE8lVgDXhEl2eH6-17" vertex="1" connectable="0">
          <mxGeometry x="-0.702" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-19" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="160" y="1249" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-22" value="8b. Inventory Increase" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="161" y="1094" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="P6dzXHE8lVgDXhEl2eH6-12" target="P6dzXHE8lVgDXhEl2eH6-22" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="1030" as="sourcePoint" />
            <mxPoint x="510" y="980" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-27" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="P6dzXHE8lVgDXhEl2eH6-22" target="P6dzXHE8lVgDXhEl2eH6-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="1080" as="sourcePoint" />
            <mxPoint x="580" y="1030" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-36" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="P6dzXHE8lVgDXhEl2eH6-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="860" as="sourcePoint" />
            <mxPoint x="627" y="520" as="targetPoint" />
            <Array as="points">
              <mxPoint x="660" y="1124" />
              <mxPoint x="660" y="520" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-37" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P6dzXHE8lVgDXhEl2eH6-36" vertex="1" connectable="0">
          <mxGeometry x="-0.6464" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.466;entryY=-0.003;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="decision2" target="partial_ship" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="537.5" y="519" as="sourcePoint" />
            <mxPoint x="552.5" y="960" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P6dzXHE8lVgDXhEl2eH6-11" value="Partial Shortage" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="P6dzXHE8lVgDXhEl2eH6-7" vertex="1" connectable="0">
          <mxGeometry x="0.2535" y="2" relative="1" as="geometry">
            <mxPoint x="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fQQUG8jzuy2Q0AqbQklD-1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="full_inventory" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="720" as="sourcePoint" />
            <mxPoint x="870" y="777" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fQQUG8jzuy2Q0AqbQklD-2" value="9a. Receive Inventory Changes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="873" y="746" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fQQUG8jzuy2Q0AqbQklD-3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="P6dzXHE8lVgDXhEl2eH6-22" target="fQQUG8jzuy2Q0AqbQklD-4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="1060" as="sourcePoint" />
            <mxPoint x="900" y="1200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="231" y="1200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fQQUG8jzuy2Q0AqbQklD-4" value="9b2. Receive Inventory Changes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="883" y="1171" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-4" value="2. Production Part？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="86" y="206" width="168" height="80" as="geometry" />
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="start1" target="iFhU12ZTh78KMy7YP24t-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="260" as="sourcePoint" />
            <mxPoint x="580" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="iFhU12ZTh78KMy7YP24t-4" target="approval1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="260" as="sourcePoint" />
            <mxPoint x="580" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-7" value="yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" connectable="0" vertex="1" parent="iFhU12ZTh78KMy7YP24t-6">
          <mxGeometry x="-0.6826" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-8" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="iFhU12ZTh78KMy7YP24t-4" target="generate_po">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="80" y="240" as="sourcePoint" />
            <mxPoint x="580" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="50" y="246" />
              <mxPoint x="50" y="445" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-11" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" connectable="0" vertex="1" parent="iFhU12ZTh78KMy7YP24t-8">
          <mxGeometry x="-0.9036" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="generate_po" target="decision2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="430" as="sourcePoint" />
            <mxPoint x="580" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="551" y="460" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="iFhU12ZTh78KMy7YP24t-12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="modify_request" target="iFhU12ZTh78KMy7YP24t-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="380" as="sourcePoint" />
            <mxPoint x="510" y="330" as="targetPoint" />
            <Array as="points">
              <mxPoint x="170" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
