<svg xmlns="http://www.w3.org/2000/svg" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" version="1.1" width="932.2030029296875" height="216.5">
          <defs>
            <style type="text/css">svg {
  background-color: #fff;
}

text, tspan {
  font: 12px Arial;
}

path {
  fill-opacity: 0;
  stroke-width: 2px;
  stroke: #000;
}

circle {
  fill: #6b6659;
  stroke-width: 2px;
  stroke: #000;
}

.anchor text, .any-character text {
  fill: #fff;
}

.anchor rect, .any-character rect {
  fill: #6b6659;
}

.escape text, .charset-escape text, .literal text {
  fill: #000;
}

.escape rect, .charset-escape rect {
  fill: #bada55;
}

.literal rect {
  fill: #dae9e5;
}

.charset .charset-box {
  fill: #cbcbba;
}

.subexp .subexp-label,
.charset .charset-label,
.match-fragment .repeat-label {
  font-size: 10px;
}

.subexp .subexp-label,
.charset .charset-label {
  dominant-baseline: text-after-edge;
}

.subexp .subexp-box {
  stroke: #908c82;
  stroke-dasharray: 6,2;
  stroke-width: 2px;
  fill-opacity: 0;
}

.quote {
  fill: #908c82;
}

/*# sourceMappingURL=svg.css.map */
</style>
          </defs>
          <metadata>
            <rdf:rdf>
              <cc:license rdf:about="http://creativecommons.org/licenses/by/3.0/">
                <cc:permits rdf:resource="http://creativecommons.org/ns#Reproduction"></cc:permits>
                <cc:permits rdf:resource="http://creativecommons.org/ns#Distribution"></cc:permits>
                <cc:requires rdf:resource="http://creativecommons.org/ns#Notice"></cc:requires>
                <cc:requires rdf:resource="http://creativecommons.org/ns#Attribution"></cc:requires>
                <cc:permits rdf:resource="http://creativecommons.org/ns#DerivativeWorks"></cc:permits>
              </cc:license>
            </rdf:rdf>
          </metadata>
        <desc>Created with Snap</desc><g transform="matrix(1,0,0,1,15,11)" class="root"><g transform="matrix(1,0,0,1,10,0)" class="regexp match"><path d="M102.703125,97.25H137.7031M158.812475,97.25H168.8125"></path><g class="match-fragment subexp" transform="matrix(1,0,0,1,0,64.5)"><rect rx="3" ry="3" transform="matrix(1,0,0,1,0,11)" width="127.703125" height="53.5" class="subexp-box"></rect><text x="0" y="0" transform="matrix(1,0,0,1,0,11)" class="subexp-label">group #1</text><g transform="matrix(1,0,0,1,10,21)" class="regexp match match-fragment"><path d="M10,11.75q-10,0 -10,10v1.75q0,10 10,10h82.703125q10,0 10,-10v-1.75q0,-10 -10,-10M102.703125,26.75l5,-5m-5,5l-5,-5"></path><g transform="matrix(1,0,0,1,10,0)" class="any-character"><g class="label"><rect width="82.703125" height="23.5"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan>any character</tspan></text></g></g></g></g><g class="match-fragment literal" transform="matrix(1,0,0,1,137.7031,85.5)"><g class="label"><rect width="21.109375" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>|</tspan><tspan class="quote">”</tspan></text></g></g><g class="match-fragment subexp regexp" transform="matrix(1,0,0,1,168.8125,0)"><path d="M10,42.75q0,-10 10,-10M703.3905029296875,42.75q0,-10 -10,-10M10,143.5q0,10 10,10M703.3905029296875,143.5q0,10 -10,10M0,97.25q10,0 10,-10V42.75M713.3905029296875,97.25q-10,0 -10,-10V42.75M0,97.25q10,0 10,10V143.5M713.3905029296875,97.25q-10,0 -10,10V143.5"></path><g transform="matrix(1,0,0,1,20,0)" class="regexp-matches"><path d="M0,32.75h280.6874M367.703025,32.75H673.3905029296875M0,153.5h40M633.390525,153.5H673.3905029296875"></path><g transform="matrix(1,0,0,1,280.6874,0)" class="match"><path d="M25,32.75H55"></path><g class="match-fragment literal" transform="matrix(1,0,0,1,0,21)"><g class="label"><rect width="25" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>+</tspan><tspan class="quote">”</tspan></text></g></g><g class="match-fragment subexp" transform="matrix(1,0,0,1,35,0)"><rect rx="3" ry="3" transform="matrix(1,0,0,1,0,11)" width="77.015625" height="53.5" class="subexp-box"></rect><text x="0" y="0" transform="matrix(1,0,0,1,0,11)" class="subexp-label">group #2</text><g transform="matrix(1,0,0,1,10,21)" class="regexp match match-fragment"><path d="M10,11.75q-10,0 -10,10v1.75q0,10 10,10h32.015625q10,0 10,-10v-1.75q0,-10 -10,-10M52.015625,26.75l5,-5m-5,5l-5,-5"></path><g transform="matrix(1,0,0,1,10,0)" class="escape"><g class="label"><rect width="32.015625" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan>digit</tspan></text></g></g></g></g></g><g transform="matrix(1,0,0,1,0,70.5)" class="match"><path d="M351.031225,83H416.0312"></path><g class="match-fragment" transform="matrix(1,0,0,1,0,0)"><path d="M0,83q10,0 10,-10v-64q0,-10 10,-10h351.0312194824219q10,0 10,10v64q0,10 10,10"></path><g class="subexp" transform="matrix(1,0,0,1,15,10)"><rect rx="3" ry="3" transform="matrix(1,0,0,1,0,11)" width="361.0312194824219" height="104" class="subexp-box"></rect><text x="0" y="0" transform="matrix(1,0,0,1,0,11)" class="subexp-label">group #3</text><g transform="matrix(1,0,0,1,10,21)" class="regexp match"><path d="M50,52H85M117.015625,52H157.0156M179.0156,52H219.0156M254.0156,52H294.0156"></path><g class="match-fragment" transform="matrix(1,0,0,1,0,0)"><path d="M0,52q10,0 10,-10v-33q0,-10 10,-10h25q10,0 10,10v33q0,10 10,10"></path><g class="charset" transform="matrix(1,0,0,1,15,10)"><rect rx="3" ry="3" transform="matrix(1,0,0,1,0,11)" width="35" height="62" class="charset-box"></rect><text x="0" y="0" transform="matrix(1,0,0,1,0,11)" class="charset-label">One of:</text><g transform="matrix(1,0,0,1,5,16)"><g class="literal" transform="matrix(1,0,0,1,0,0)"><g class="label"><rect width="25" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>+</tspan><tspan class="quote">”</tspan></text></g></g><g class="literal" transform="matrix(1,0,0,1,1.5,28.5)"><g class="label"><rect width="22" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>-</tspan><tspan class="quote">”</tspan></text></g></g></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,75,40.25)"><path d="M10,11.75q-10,0 -10,10v1.75q0,10 10,10h32.015625q10,0 10,-10v-1.75q0,-10 -10,-10M52.015625,26.75l5,-5m-5,5l-5,-5"></path><g class="escape" transform="matrix(1,0,0,1,10,0)"><g class="label"><rect width="32.015625" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan>digit</tspan></text></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,142.0156,30.25)"><path d="M0,21.75q10,0 10,-10v-1.75q0,-10 10,-10h12q10,0 10,10v1.75q0,10 10,10"></path><g class="literal" transform="matrix(1,0,0,1,15,10)"><g class="label"><rect width="22" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>-</tspan><tspan class="quote">”</tspan></text></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,204.0156,0)"><path d="M0,52q10,0 10,-10v-33q0,-10 10,-10h25q10,0 10,10v33q0,10 10,10"></path><g class="charset" transform="matrix(1,0,0,1,15,10)"><rect rx="3" ry="3" transform="matrix(1,0,0,1,0,11)" width="35" height="62" class="charset-box"></rect><text x="0" y="0" transform="matrix(1,0,0,1,0,11)" class="charset-label">One of:</text><g transform="matrix(1,0,0,1,5,16)"><g class="literal" transform="matrix(1,0,0,1,0,0)"><g class="label"><rect width="25" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>+</tspan><tspan class="quote">”</tspan></text></g></g><g class="literal" transform="matrix(1,0,0,1,1.5,28.5)"><g class="label"><rect width="22" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>-</tspan><tspan class="quote">”</tspan></text></g></g></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,279.0156,30.25)"><path d="M0,21.75q10,0 10,-10v-1.75q0,-10 10,-10h22.015625q10,0 10,10v1.75q0,10 10,10M15,21.75q-10,0 -10,10v1.75q0,10 10,10h32.015625q10,0 10,-10v-1.75q0,-10 -10,-10M57.015625,36.75l5,-5m-5,5l-5,-5"></path><g class="escape" transform="matrix(1,0,0,1,15,10)"><g class="label"><rect width="32.015625" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan>digit</tspan></text></g></g></g></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,401.0312,30.25)"><path d="M0,52.75q10,0 10,-10v-33.75q0,-10 10,-10h232.35931396484375q10,0 10,10v33.75q0,10 10,10"></path><g class="subexp regexp match" transform="matrix(1,0,0,1,15,10)"><path d="M21.328125,42.75H51.3281"></path><g class="match-fragment literal" transform="matrix(1,0,0,1,0,31)"><g class="label"><rect width="21.328125" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>.</tspan><tspan class="quote">”</tspan></text></g></g><g class="match-fragment subexp" transform="matrix(1,0,0,1,31.3281,0)"><rect rx="3" ry="3" transform="matrix(1,0,0,1,0,11)" width="211.03121948242188" height="63.5" class="subexp-box"></rect><text x="0" y="0" transform="matrix(1,0,0,1,0,11)" class="subexp-label">group #4</text><g transform="matrix(1,0,0,1,10,21)" class="regexp match"><path d="M42.015625,21.75H82.0156M104.0156,21.75H144.0156"></path><g class="match-fragment" transform="matrix(1,0,0,1,0,10)"><path d="M10,11.75q-10,0 -10,10v1.75q0,10 10,10h32.015625q10,0 10,-10v-1.75q0,-10 -10,-10M52.015625,26.75l5,-5m-5,5l-5,-5"></path><g class="escape" transform="matrix(1,0,0,1,10,0)"><g class="label"><rect width="32.015625" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan>digit</tspan></text></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,67.0156,0)"><path d="M0,21.75q10,0 10,-10v-1.75q0,-10 10,-10h12q10,0 10,10v1.75q0,10 10,10"></path><g class="literal" transform="matrix(1,0,0,1,15,10)"><g class="label"><rect width="22" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan class="quote">“</tspan><tspan>-</tspan><tspan class="quote">”</tspan></text></g></g></g><g class="match-fragment" transform="matrix(1,0,0,1,129.0156,0)"><path d="M0,21.75q10,0 10,-10v-1.75q0,-10 10,-10h22.015625q10,0 10,10v1.75q0,10 10,10M15,21.75q-10,0 -10,10v1.75q0,10 10,10h32.015625q10,0 10,-10v-1.75q0,-10 -10,-10M57.015625,36.75l5,-5m-5,5l-5,-5"></path><g class="escape" transform="matrix(1,0,0,1,15,10)"><g class="label"><rect width="32.015625" height="23.5" rx="3" ry="3"></rect><text x="0" y="0" transform="matrix(1,0,0,1,5,16.75)"><tspan>digit</tspan></text></g></g></g></g></g></g></g></g></g></g></g><path d="M30,97.25H0M892.2030029296875,97.25H902.2030029296875"></path><circle cx="0" cy="97.25" r="5"></circle><circle cx="902.2030029296875" cy="97.25" r="5"></circle></g></svg>