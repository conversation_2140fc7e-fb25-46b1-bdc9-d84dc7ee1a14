***\*Sales Module External Integration\****

**1** 

**2** 

**3** 

**4** 

**5** 

5.1 

**5.1.1** ***\*Integration of DMS and Super APP\****

*********** ***\*Integration Description\****

Integration of DMS and Super APP includes basic data, vehicle sales order list, order details, and payment/refund data.

*********** 

|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml25500\wps6.jpg) |

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                           |
| ------------ | ------------------------- | ------------------------------------------------------------ |
| 1            | Outlet Info                  | DMS provides basic outlet information to SUPER APP for customer inquiry and selection. |
| 2            | SA Info              | DMS provides detailed SA information to SUPER APP, making it convenient for customers to choose a suitable advisor. |
| 3            | Vehicle Model Info              | DMS syncs vehicle model info to SUPER APP, including model, variant, color, etc. |
| 4            | Vehicle Price Info              | DMS provides real-time vehicle price info to SUPER APP, including base price and optional accessory prices. |
| 5            | Basic Data Display              | SUPER APP receives and displays the outlet, SA, vehicle model, and price info pushed by DMS. |
| 6            | Order Creation/Editing             | Customer selects vehicle model, outlet, and SA in SUPER APP to create or edit an order. |
| 7            | Order Info Sync              | DMS receives order info from SUPER APP, including vehicle model, payment method, etc. |
| 8            | Booking Fee Calculation              | DMS calculates and provides the booking fee information to SUPER APP. |
| 10           | Payment Result Reception              | DMS receives payment result information from SUPER APP and updates the order's payment status. |
| 11           | Sync Payment Status              | SUPER APP syncs payment processing results to DMS to ensure status consistency on both ends. |
| 12           | Provide Order Details              | DMS provides customer's detailed order information to SUPER APP, including status, configuration, price, etc. |
| 14           | Personal Info Change              | Customer modifies personal info in SUPER APP and submits a change request to DMS for review. |

 

 

 

 

 

 

 

 

 

 

**5.1.2** ***\*Integration of DMS and ERP\****

*********** ***\*Integration Description\****

Integration of DMS and ERP includes vehicle master data, Vehicle info, sales order data, payment/refund information, delivery data, and invoice data.

*********** 

|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml25500\wps7.jpg) |

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                   |
| ------------ | ------------------------- | ---------------------------------------------------- |
| 1            | Vehicle Master Data | ERP syncs vehicle master data to DMS to provide basic information for vehicle management. |
| 2            | Vehicle Info       | ERP syncs vehicle info (VIN) to DMS for vehicle identification and management. |
| 4            | Inventory Info     | DMS sends inbound info back to ERP to ensure inventory data synchronization. |
| 5            | Order Info         | After an order is created in DMS, it syncs the order info to ERP for order management. |
| 6            | Payment/Refund Info       | DMS sends payment/refund info to ERP to sync financial status. |
| 7            | Delivery Info            | DMS syncs order delivery info to ERP to complete the vehicle delivery. |
| 8            | Invoice Info       | ERP syncs invoice info to DMS to complete the invoice management process. |

 

**5.1.3** ***\*Integration of DMS and VIPRO\****

*********** ***\*Integration Description\****

Integration of DMS and VIPRO system includes insurance application information.

*********** 

|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml25500\wps8.jpg) |

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                           |
| ------------ | ------------------------- | ------------------------------------------------------------ |
| 1            | Insurance Application     | DMS initiates a vehicle insurance application, including customer's personal information and vehicle information. |
| 2            | Insurance Result          | VIPRO System returns the vehicle insurance result, including the insurance status. DMS receives the result and updates the corresponding business status. |

 

**5.1.4** ***\*Integration of DMS and JPJ\****

*********** ***\*Integration Description\****

Integration of DMS and JPJ includes vehicle registration information.

*********** 

|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml25500\wps9.jpg) |

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\****    | ***\*Activity\****                            |
| ------------ | ---------------------------- | --------------------------------------------- |
| 1            | Vehicle Registration Request | DMS initiates a vehicle registration request, containing vehicle and owner related information. |
| 2            | Registration Result          | JPJ returns the vehicle registration result, including registration status and related information. |

 

***\*5.2\**** ***\*Service Module External Integration\****

**5.1.5** ***\*Integration of DMS and Super APP\****

*********** ***\*Integration Description\****

Integration of DMS and Super APP includes basic data, service appointment data, maintenance packages, appointment cancellations, and appointment payment data.

*********** 

|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml25500\wps10.jpg) |

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                     |
| ------------ | ------------------------- | ------------------------------------------------------ |
| 1            | Outlet Info         | DMS provides outlet info to SUPER APP for customer inquiry and selection. |
| 2            | Customer Vehicle History  | DMS provides customer vehicle history to SUPER APP for service recommendations. |
| 3            | Available Time Slots      | DMS provides outlet's available time slots to SUPER APP to display bookable times. |
| 4            | Maintenance Package       | DMS provides maintenance package and price information to SUPER APP for customers to view and select. |
| 5            | Appointment Info   | DMS receives appointment creation info from SUPER APP and processes the appointment request. |
| 6            | Package Price Info | DMS provides the price information of the selected package to SUPER APP for payment confirmation. |
| 7            | Payment Info       | DMS receives payment info from SUPER APP to confirm payment status. |
| 8            | Sync Payment info              | SUPER APP syncs payment info to DMS to maintain payment status consistency. |
| 9            | Appointment List          | DMS provides customer's appointment list to SUPER APP to display appointment records. |
| 10           | Appointment Details       | DMS provides customer's appointment details to SUPER APP to display detailed content. |
| 11           | Cancellation Appointment info | DMS receives cancellation info from SUPER APP and processes the cancellation request. |
| 12           | Refund Info        | DMS receives refund info from SUPER APP and processes the refund. |

 

 

 

**5.1.6** ***\*Integration of DMS and ERP\****

*********** ***\*Integration Description\****

Integration of DMS and ERP includes service work order payments, refund information, parts requisition data, inbound information, and parts usage data.

*********** 

|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml25500\wps11.jpg) |

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                     |
| ------------ | ------------------------- | -------------------------------------- |
| 1            | Payment/Refund Info       | DMS sends payment/refund info to ERP to sync financial status. |
| 2            | Parts Requisition             | DMS creates a parts requisition to request necessary parts from ERP. |
| 3            | Inventory Info     | DMS receives goods and syncs inventory change information to ERP. |
| 4            | Parts Usage               | DMS syncs parts usage information from work orders to ERP.   |

 