<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="3">
  <diagram name="第 1 页" id="pAn5I8VzCeUgmV9Bc8eY">
    <mxGraphModel dx="1388" dy="630" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-1" value="客户" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="60" width="360" height="1010" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-2" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="60" width="560" height="1010" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-3" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="180" y="120" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-4" value="1. 客户到达门店&lt;br&gt;(自然进店)" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="220" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-5" value="1. 在DMS查询车辆信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="220" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-6" value="1. 填写/确认到店登记单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="320" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-7" value="是否保存登记单?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="518" y="420" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-8" value="2b. 取消登记" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="758" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-9" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="778" y="540" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-11" target="dCRLqZLZSVvJEKh9Vo9m-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-11" value="2a. 保存到店登记单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="580" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-13" target="dCRLqZLZSVvJEKh9Vo9m-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-13" value="4a. 创建关联环检单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="790" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-14" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="578" y="880" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-3" target="dCRLqZLZSVvJEKh9Vo9m-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="528" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-5" target="dCRLqZLZSVvJEKh9Vo9m-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-6" target="dCRLqZLZSVvJEKh9Vo9m-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-7" target="dCRLqZLZSVvJEKh9Vo9m-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-20" value="否 / 取消" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="688" y="440" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-8" target="dCRLqZLZSVvJEKh9Vo9m-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-7" target="dCRLqZLZSVvJEKh9Vo9m-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-23" value="是 / 保存" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="588" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="dCRLqZLZSVvJEKh9Vo9m-25" target="dCRLqZLZSVvJEKh9Vo9m-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dCRLqZLZSVvJEKh9Vo9m-25" value="3a. 查询、编辑店登记单列表&lt;div&gt;或详情&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="680" width="180" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="English Version" id="eN8L9sKvCdRmT5xWz2A7">
    <mxGraphModel dx="1665" dy="756" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-1" value="Customer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="60" width="360" height="1010" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-2" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="60" width="560" height="1010" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-3" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="180" y="120" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-4" value="1. Customer at store&lt;br&gt;(walk-in)" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="220" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-5" value="1. Query vehicle information" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="220" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-6" value="1. Fill out/confirm arrival registration form" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="320" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-7" value="Save registration form?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="518" y="420" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-8" value="2b. Cancel registration" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="803" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-9" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="822" y="540" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-11" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-11" value="2a. Save arrival registration form" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="580" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-13" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-13" value="4a. Create associated inspection form" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="790" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-14" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="578" y="880" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-3" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="528" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-5" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-6" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-7" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Lx-z5pacY0B7mIITmWDV-1" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="eN8L-dCRLqZLZSVvJEKh9Vo9m-19" vertex="1" connectable="0">
          <mxGeometry x="-0.0235" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-8" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-22" value="&amp;nbsp;Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-7" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="eN8L-dCRLqZLZSVvJEKh9Vo9m-25" target="eN8L-dCRLqZLZSVvJEKh9Vo9m-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eN8L-dCRLqZLZSVvJEKh9Vo9m-25" value="3a. Query, edit store registration&lt;div&gt;list or details&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="528" y="680" width="180" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="X0YYs0ChJYP0HXrBAmXL" name="第 3 页">
    <mxGraphModel dx="951" dy="432" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-1" value="Customer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
          <mxGeometry x="20" y="20" width="360" height="1010" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-2" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" vertex="1" parent="1">
          <mxGeometry x="380" y="20" width="560" height="1010" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-3" value="Start" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="160" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-4" value="1. Walk in outlet" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="110" y="180" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-5" value="2. Query vehicle information" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="508" y="180" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-6" value="3. Create On-Site Registration Form" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="508" y="280" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="nKrFrzCBsbqLPUpx_Xyk-13" target="nKrFrzCBsbqLPUpx_Xyk-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-13" value="5. Create Inspection form" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="508" y="490" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-14" value="End" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="558" y="580" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="nKrFrzCBsbqLPUpx_Xyk-3" target="nKrFrzCBsbqLPUpx_Xyk-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="nKrFrzCBsbqLPUpx_Xyk-4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="508" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="nKrFrzCBsbqLPUpx_Xyk-5" target="nKrFrzCBsbqLPUpx_Xyk-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nKrFrzCBsbqLPUpx_Xyk-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="nKrFrzCBsbqLPUpx_Xyk-6">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="598" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ccFtxCPL4nR1LpXfmH7a-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ccFtxCPL4nR1LpXfmH7a-1" target="nKrFrzCBsbqLPUpx_Xyk-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ccFtxCPL4nR1LpXfmH7a-1" value="4. Select&amp;nbsp; &quot;Repair&quot;as the service type" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="508" y="380" width="180" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
