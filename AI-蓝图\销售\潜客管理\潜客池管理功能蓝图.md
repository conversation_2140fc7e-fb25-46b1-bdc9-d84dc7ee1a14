# 潜客池管理功能蓝图

| 序号 | 动作 | 涉及文件（输入/输出） | 描述 |
|:---|:---|:---|:---|
| 1 | 判断潜客是否注册APP | | 销售顾问首先需要判断潜客是否已经通过Super APP注册，以决定后续的跟进方式。这是一个决策节点，引出两种不同的处理路径。 |
| 1a | 在厂端潜客池查询到潜客信息 | | 如果潜客已注册APP，销售顾问可以通过DMS-店端系统在DMS-厂端潜客池中查询并获取该潜客的资料。 |
| 1b | 销售顾问手动创建潜客档案 | 潜客档案信息 | 对于未注册APP的潜客，销售顾问需要在DMS-店端手动创建潜客档案，录入客户的基本信息。 |
| 1b1 | 客户注册/使用Super APP | | 客户通过Super APP完成注册或登录，这是客户旅程的起点。 |
| 1b2 | 潜客信息进入厂端潜客池 | | 客户在Super APP注册后，其信息会自动同步到DMS-厂端的潜客池中，进行统一管理。 |
| 1b3 | 客户选择门店及顾问 | | 客户在APP中可以根据自己的地理位置或偏好，选择服务的门店以及指定的销售顾问。 |
| 2 | 判断是否已被同门店销售顾问关联 | | 系统自动检查该潜客是否已被当前门店的其他销售顾问认领或关联。如果是，则当前跟进流程结束，以避免重复打扰。 |
| 2b | 编辑潜客档案 | | 销售顾问可以对潜客档案进行编辑和完善，补充更多详细信息，如联系方式、具体的购车需求、家庭情况等。 |
| 3 | 销售顾问进行潜客跟进 | | 销售顾问通过电话、消息、面谈等多种方式与潜客进行沟通，深入了解其购车需求和潜在问题。 |
| 4 | 更新意向级别 (H/A/B/C) | | 销售顾问根据跟进情况，在系统中更新潜客的意向级别（H/A/B/C），以动态反映客户的购买意愿强度。 |
| 5 | 试驾管理 | | 如果客户有试驾需求，销售顾问将通过试驾管理模块为客户安排试驾，包括预约、车辆准备和记录试驾反馈。 |
| 6 | 判断客户意向 | | 经过一系列跟进和互动后，销售顾问需要对客户的最终购买意向做出判断：有意向、暂不明确或明确无购买意向。 |
| 6a | 订单管理 | | 对于明确有购买意向的客户，销售顾问将引导其进入订单管理流程，开始创建和处理购车订单。 |
| 6a1 | 同步订单和潜客信息到门店 | | 客户在APP端创建订单后，系统会将订单信息和更新后的潜客档案同步到对应的DMS-店端。 |
| 6a2 | 客户选择车型创建订单 | | 客户在Super APP中浏览车型配置，选择心仪的车型并创建预订订单。 |
| 6a3 | 客户支付定金 | | 客户通过Super APP内的支付功能完成订单定金的支付，以确认其购买意向。 |
| 6a4 | 订单预订成功 | | 定金支付成功后，系统后台确认订单预订成功，并正式更新订单状态。 |
| 6c | 更新潜客状态为"战败" | | 对于明确表示无购买意向的客户，销售顾问将其状态在系统中标记为"战败"，并记录战败原因。 |
| 7 | 判断是否有其他顾问重新跟进 | | 对于战败潜客，系统会判断是否有其他销售顾问愿意接手并重新尝试跟进激活。 |
| 7a | 其他销售顾问跟进 | | 如果有其他销售顾问选择接手，该潜客将进入新的跟进流程，由新顾问尝试重新激活其购买意向。 |
| 8 | 更新潜客档案状态 | | 客户在APP下单成功后，DMS-厂端系统会根据订单状态自动更新潜客档案的跟进状态和意向级别，标记为已转化。 |

## 潜客池管理功能蓝图 - English Version

| No. | Action | Involved Files (Input/Output) | Description |
|:---|:---|:---|:---|
| 1 | Check if prospect is registered on APP | | The SA needs to check if the prospect has registered via the Super APP. |
| 1a | Find prospect info in HQ Prospect Pool | | If the prospect has registered for the APP, the SA can query and obtain the prospect's profile from the HQ Prospect Pool. |
| 1b | SA manually creates Prospect File | | For prospects not registered on the APP, the SA needs to manually create a prospect file in the DMS, entering the customer's basic information. |
| 1b1 | Customer registers/uses Super APP | | The customer registers the Super APP, which is the start of the customer journey. |
| 1b2 | Prospect info enters HQ Prospect Pool | | After the customer registers on the Super APP, their information is automatically synced to the HQ Prospect Pool. |
| 1b3 | Customer selects outlet and SA | | In the APP, the customer can select a service outlet and a specific SA based on their location or preference. |
| 2 | Check if associated with SA from the same outlet | | The system automatically checks if the prospect has already been followed-up by another SA from the current outlet. If so, the SA can not continue to  follow-up the same prospect. |
| 2b | Edit Prospect File | | The SA can edit the prospect file, adding more detailed information such as contact details, specific car needs, and family situation. |
| 3 | SA follows up with prospect | | The SA communicates with the prospect through various ways like phone calls, messages, or social media to understand their car-buying needs. |
| 4 | Update Intention Level (H/A/B/C) | | Based on the follow-up, the SA updates the prospect's intention level (H/A/B/C) in the system to dynamically reflect the strength of their purchase intent. |
| 5 | Test Drive Management | | If the customer requests a test drive, the SA will arrange it through the Test Drive Management module and recording feedback. |
| 6 | Determine prospect's intention | | After a series of follow-ups and interactions, the SA needs to determine the customer's final purchase intention: Intention, Uncertain, or No intention. |
| 6a | Order Management | | For customers with a clear purchase intention, the SA will guide them to book car and creat the sales order. |
| 6a1 | Sync order and prospect info to outlet | | After the customer creates an order on the APP, the DMS syncs the order information and the updated prospect file to the corresponding Outlet. |
| 6a2 | Customer selects model and creates order | | The customer browses model configurations in the Super APP, selects a preferred model, and books the car. |
| 6a3 | Customer pays booking fee | | The customer pays booking fee in the Super APP to confirm the purchase intention. |
| 6a4 | Vehicle reservation successful | | After the booking fee is paid successfully, the DMS confirms the vehicle reservation and updates the order status. |
| 6c | Update prospect status to "Lost" | | For customers who have clearly no purchase intention, the SA marks their status as "Lost " in the system and records the reason. |
| 7 | Check if another SA will re-engage | | For lost prospect, if a SA wants to follow up again, the system will check whether another SA has already followed up. |
| 7a | Another SA follows up | | If another SA want to follow-up,  then enter a new follow-up process. The new SA will try to reactivate the prospect's purchase intention. |
| 8 | Update Prospect File status | | After the customer successfully book vehicle on the APP, the HQ also automatically updates the prospect file's follow-up status and intention level based on the order status. |