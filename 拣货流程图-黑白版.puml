@startuml 拣货流程图
!theme plain
skinparam title {
    RoundCorner 15
    BorderThickness 2
    BorderColor #000000
    BackgroundColor #FFFFFF
    FontColor #000000
    FontSize 24
}
skinparam swimlane {
    BorderColor #000000
    BorderThickness 2
    TitleFontColor #000000
    TitleBackgroundColor #FFFFFF
    TitleFontSize 16
}
skinparam activity {
    BorderColor #000000
    BackgroundColor #FFFFFF
    FontColor #000000
    Shadowing false
}
skinparam note {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
}
skinparam arrow {
    Color #000000
    Thickness 2
}

title 拣货流程图

|DMS-店端|
start
:系统生成拣货单;
note right: 工单客户确认后

label 库存检查
:系统判断门店零件库存;

if (库存是否满足?) then (满足)
  :门店操作员执行拣货;
  :门店操作员点击拣货完成;
  :系统扣减门店库存;
  
  label 退料检查
  if (技师是否需要退料?) then (需要)
    :技师执行退料操作;
    :系统增加零件库存;
    stop
  else (不需要)
    stop
  endif
else (不满足)
  :拣货单显示缺货状态;
  :门店操作员创建叫料清单;
  
  |DMS-厂端|
  :HQ审批员审批叫料清单;
  
  |ERP系统|
  :系统生成采购订单并发货;
  
  |DMS-店端|
  :门店操作员完成收货;
  :系统自动锁定可用库存;
  note right: 锁定相应零件
  :门店操作员点击拣货完成;
  :系统扣减门店库存;
  goto 退料检查
endif

@enduml
