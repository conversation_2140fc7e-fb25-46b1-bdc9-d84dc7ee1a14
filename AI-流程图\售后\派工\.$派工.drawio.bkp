<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="2">
  <diagram name="第 1 页" id="h72RnlhiS8HJ98mNYIsZ">
    <mxGraphModel dx="1261" dy="753" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="eFkDOxf_kof-DLVDV4of-1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="30" width="1000" height="1490" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-12" target="eFkDOxf_kof-DLVDV4of-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-3" value="暂停" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-13" target="eFkDOxf_kof-DLVDV4of-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-4" value="继续" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-13" target="eFkDOxf_kof-DLVDV4of-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-15" target="eFkDOxf_kof-DLVDV4of-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="1240" />
              <mxPoint x="500" y="1240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-16" target="eFkDOxf_kof-DLVDV4of-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-17" target="eFkDOxf_kof-DLVDV4of-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="1450" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-33" target="eFkDOxf_kof-DLVDV4of-20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-9" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-27" target="eFkDOxf_kof-DLVDV4of-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-10" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-27" target="eFkDOxf_kof-DLVDV4of-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="790" y="950" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-12" value="6a. 执行维修保养工作" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="1020" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-13" value="工作状态控制?" style="rhombus;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="420" y="1110" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-14" value="7b1.暂停工作" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="700" y="1020" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-15" value="6a. 继续工作" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="100" y="1120" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-16" value="线下通知技师经理工作完成" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="1210" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-17" value="&amp;nbsp;8. 确认完工并check工时与零件，更新工单状态为待质检" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="1300" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-18" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="460" y="1410" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-20" target="eFkDOxf_kof-DLVDV4of-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-20" value="2. 选择待分配工单，并点击分配钮" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="290" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-22" target="eFkDOxf_kof-DLVDV4of-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-22" value="3. 根据工单技术要求、&lt;div&gt;技师空闲时间进行选择&lt;div&gt;并设置预计开工时间&lt;/div&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="420" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-24" target="eFkDOxf_kof-DLVDV4of-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-24" value="3. 确认分配" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="540" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-26" target="eFkDOxf_kof-DLVDV4of-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-26" value="&lt;font&gt;&amp;nbsp;4. 技师查&lt;span style=&quot;font-family: 黑体;&quot;&gt;个人任务列表&lt;/span&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="670" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-27" value="&amp;nbsp;是否需要更换技师" style="rhombus;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="420" y="800" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-29" target="eFkDOxf_kof-DLVDV4of-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-29" value="5. 打卡开工" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="100" y="810" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-31" target="eFkDOxf_kof-DLVDV4of-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-31" value="6b. 重新分配工单" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="700" y="810" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-33" target="eFkDOxf_kof-DLVDV4of-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-33" value="1. 查询工单列表、详情" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="380" y="180" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-34" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="460" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-34" target="eFkDOxf_kof-DLVDV4of-33" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="190" as="targetPoint" />
            <mxPoint x="500" y="160" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=0;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-38" target="eFkDOxf_kof-DLVDV4of-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-38" target="eFkDOxf_kof-DLVDV4of-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-38" value="7b2. 恢复工作" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="700" y="920" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-39" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=2;" parent="eFkDOxf_kof-DLVDV4of-1" source="eFkDOxf_kof-DLVDV4of-14" target="eFkDOxf_kof-DLVDV4of-38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="830" y="990" as="sourcePoint" />
            <mxPoint x="830" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eFkDOxf_kof-DLVDV4of-40" value="9. 查看派工看板" style="rounded=1;whiteSpace=wrap;html=1;" parent="eFkDOxf_kof-DLVDV4of-1" vertex="1">
          <mxGeometry x="680" y="180" width="240" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="English Version" id="en_dispatch_flow">
    <mxGraphModel dx="860" dy="514" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="en_0" />
        <mxCell id="en_1" parent="en_0" />
        <mxCell id="en_swimlane_1" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="en_1" vertex="1">
          <mxGeometry x="40" y="30" width="1000" height="1490" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_12" target="en_cell_13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_3" value="Pause" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_13" target="en_cell_14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_4" value="Continue" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_13" target="en_cell_15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_15" target="en_cell_16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="1240" />
              <mxPoint x="500" y="1240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="en_edge_6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_16" target="en_cell_17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="en_swimlane_1" source="en_cell_17" target="en_cell_18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="1450" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="en_edge_8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_33" target="en_cell_20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_9" value="No" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_27" target="en_cell_12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_10" value="Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_27" target="en_cell_31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="790" y="950" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="en_cell_12" value="6a. Perform maintenance and repair work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="1020" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_13" value="Work status" style="rhombus;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="420" y="1110" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_14" value="7b1. Pause work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="700" y="1020" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_15" value="6a. Continue work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="100" y="1120" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_16" value="Offline notification to technician manager of work completion" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="1210" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_17" value="8. Confirm completion and check labor and parts, update work order status to pending quality inspection" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="1300" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_18" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="460" y="1410" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_20" target="en_cell_22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_20" value="2. Select pending work order and click assign button" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="290" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_22" target="en_cell_24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_22" value="3. Select based on work order technical requirements and technician availability, set expected start time" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="420" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_24" target="en_cell_26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_24" value="4. Confirm assignment" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="540" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_26" target="en_cell_29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_26" value="5. Technician checks personal task list" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="670" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_27" value="need to change the technician?" style="rhombus;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="420" y="800" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_29" target="en_cell_27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_29" value="6. Check in to start work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="100" y="810" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_31" target="en_cell_22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_31" value="6b. Reassign work order" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="700" y="810" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_33" target="en_cell_40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_33" value="1. Query work order list and details" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="380" y="180" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_34" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="460" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="en_swimlane_1" source="en_cell_34" target="en_cell_33" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="190" as="targetPoint" />
            <mxPoint x="500" y="160" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="en_edge_36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=0;" parent="en_swimlane_1" source="en_cell_38" target="en_cell_12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_38" target="en_cell_12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_38" value="7b2. Resume work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="700" y="920" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="en_edge_39" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=2;" parent="en_swimlane_1" source="en_cell_14" target="en_cell_38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="830" y="990" as="sourcePoint" />
            <mxPoint x="830" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="en_cell_40" value="7. View dispatch dashboard" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="680" y="180" width="240" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
