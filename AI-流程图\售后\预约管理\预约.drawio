<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0">
  <diagram id="mS8CYR-VctOxne8mXUTu" name="第 3 页">
    <mxGraphModel dx="1416" dy="640" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="To9AExMCa8_b3IeFiJL6-1" value="SUPER APP" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="30" y="20" width="360" height="826" as="geometry" />
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-2" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="390" y="20" width="600" height="826" as="geometry" />
        </mxCell>
        <mxCell id="ipQiedNnve4nOxP6uwD5-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="To9AExMCa8_b3IeFiJL6-4" target="To9AExMCa8_b3IeFiJL6-36" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="222" y="307" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-4" value="2a. Make appointment&lt;br&gt;(Maintenance/Repair)" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="132" y="174" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-6" value="5a. Scan QR code to check in" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="128" y="408.5" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="To9AExMCa8_b3IeFiJL6-10" target="rCRQ28B3-wCzkwc5MZoW-25" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="570" y="550" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-10" value="5b. Auto update status to &quot;Late&quot;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="599" y="520" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="To9AExMCa8_b3IeFiJL6-14" target="rCRQ28B3-wCzkwc5MZoW-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-14" value="7. Create inspection order" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="646.5" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ipQiedNnve4nOxP6uwD5-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="To9AExMCa8_b3IeFiJL6-31" target="To9AExMCa8_b3IeFiJL6-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-31" value="1. Set the limited quota of available appointments" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="174" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ipQiedNnve4nOxP6uwD5-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="To9AExMCa8_b3IeFiJL6-33" target="To9AExMCa8_b3IeFiJL6-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-33" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="650" y="85" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3w3H5sFdEiyC6n-7K25e-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="To9AExMCa8_b3IeFiJL6-36" target="3w3H5sFdEiyC6n-7K25e-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="To9AExMCa8_b3IeFiJL6-36" value="3. Create appointment order" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="408" y="277" width="157" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tyY_HNNDtr_sG9QVluTn-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3w3H5sFdEiyC6n-7K25e-1" target="tyY_HNNDtr_sG9QVluTn-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3w3H5sFdEiyC6n-7K25e-1" value="4. View appointment dashboard" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="277" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tyY_HNNDtr_sG9QVluTn-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="tyY_HNNDtr_sG9QVluTn-1" target="To9AExMCa8_b3IeFiJL6-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-23" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="tyY_HNNDtr_sG9QVluTn-3" vertex="1" connectable="0">
          <mxGeometry x="-0.7171" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="tyY_HNNDtr_sG9QVluTn-1" target="To9AExMCa8_b3IeFiJL6-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-24" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="rCRQ28B3-wCzkwc5MZoW-12" vertex="1" connectable="0">
          <mxGeometry x="-0.3001" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tyY_HNNDtr_sG9QVluTn-1" value="&lt;div&gt;5.Does the customer arrive on time?&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="601" y="398" width="178" height="81" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-7" value="Walk-in Customer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="990" y="20" width="360" height="826" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="rCRQ28B3-wCzkwc5MZoW-8" target="rCRQ28B3-wCzkwc5MZoW-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-8" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1130" y="85" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="rCRQ28B3-wCzkwc5MZoW-9" target="rCRQ28B3-wCzkwc5MZoW-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-9" value="2b. Walk in outlet&lt;br&gt;(Repair)" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1080" y="174" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="rCRQ28B3-wCzkwc5MZoW-10" target="To9AExMCa8_b3IeFiJL6-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-10" value="6. Create On-Site Registration Form" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="520" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-21" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="850" y="758" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="rCRQ28B3-wCzkwc5MZoW-25" target="To9AExMCa8_b3IeFiJL6-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-28" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="rCRQ28B3-wCzkwc5MZoW-27" vertex="1" connectable="0">
          <mxGeometry x="-0.3196" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="rCRQ28B3-wCzkwc5MZoW-25" target="rCRQ28B3-wCzkwc5MZoW-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="570" y="783" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-30" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="rCRQ28B3-wCzkwc5MZoW-29" vertex="1" connectable="0">
          <mxGeometry x="-0.795" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rCRQ28B3-wCzkwc5MZoW-25" value="&lt;div&gt;5b1.Does the customer arrive?&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="481" y="636" width="178" height="81" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
