### ![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps1.jpg)**4.2.1 Service Appointment Registration Management**

 

 

| **No.** | **Activity**          | **Documents** | **Description**                                              |
| ------- | --------------------- | ------------- | ------------------------------------------------------------ |
| 1       | 设置可用预约配额限制  |               | The outlet sets appointment quota limits for each time slot to control the number of appointments. |
| 2a      | 创建预约（保养/维修） |               | Customer makes an appointment through SUPER APP by selecting service/outlet/time, supporting both maintenance and repair service types. |
| 2b      | 到店（维修）          |               | Customer goes directly to the outlet for repair service without an appointment (Walk-in). |
| 3       | 创建预约订单          | Service Appointment Order | The system receives the customer's appointment information and automatically creates a Service Appointment Order with an initial status of "Not Arrived". |
| 4       | 查看预约仪表板        |               | View service appointment board for statistics and appointment lists. |
| 5       | 客户是否准时到达？    |               | The system determines if the customer has arrived on time based on the appointment. |
| 5a      | 扫描二维码签到        |               | Customer scans the outlet QR code to check in upon arrival, and the system updates the appointment status to "Arrived / Checked-in". |
| 5b      | 自动更新状态为'迟到'  |               | If the customer does not arrive by the appointment time, the system automatically updates the status to "Late". |
| 5b1     | 客户是否到达？        |               | The system continues to monitor if the late customer eventually arrives. |
| 6       | 创建现场登记表        | Arrival Registration Form | Create an arrival registration form for walk-in customers to record basic information. |
| 7       | 是否取消到店登记？    |               | Determines if the arrival registration needs to be cancelled.      |
| 8       | 创建检验订单          | Inspection Order | For "Arrived / Checked-in" customers, an inspection order is created to begin the service process. |

 

 

### **4.2.2 Vehicle Inspection Management**![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps2.jpg)

| **No.** | **Activity**     | **Documents**      | **Description**                                              |
| ------- | ---------------- | ------------------ | ------------------------------------------------------------ |
| 1       | 创建环检单       | Vehicle Inspection Form | SvA creates a Vehicle Inspection Form for the arrived customer and enters basic information. |
| 2       | 环检进行中       |                    | SvA fills out the EV vehicle inspection checklist, recording the vehicle's inspection results. |
| 3       | 提交并确认       |                    | After completing the inspection content, submit it. The system generates an electronic Vehicle Inspection Form and pushes it to the customer's Super APP. |
| 4       | 撤回并重新编辑   |                    | SvA can withdraw a "Pending Confirmation" Vehicle Inspection Form, rolling its status back to "Inspection in Progress" for re-editing. |
| 5       | 是否撤回？       |                    | The system determines if the Vehicle Inspection Form needs to be withdrawn for modification. |
| 6       | 客户确认方式？   |                    | The system follows different processing paths based on whether the customer chooses online or offline confirmation. |
| 6a      | 打印车辆环检单   | Output: PDF file   | Generate a paper Vehicle Inspection Form for the customer to review and sign. |
| 6a1     | 上传客户签名照片 | Input: Customer's signed photo | SvA uploads a photo of the customer's signed paper Vehicle Inspection Form to complete the offline confirmation process. |
| 6b      | 查看并确认环检单 |                    | [Customer Action] Customer views the inspection results on the Super APP and completes the online confirmation. |
| 7       | 创建工单         | Output: Work Order | After the customer confirms the Vehicle Inspection Form, the SvA creates a Work Order to proceed with the subsequent service process. |

 

### ![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps3.jpg)**4.2.3 Work Order Management**

 



|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps4.jpg) |

 



| **No.** | **Activity**             | **Documents** | **Description**                                              |
| ------- | ------------------------ | ------------- | ------------------------------------------------------------ |
| 1       | 创建工单                 |               | SvA creates a new Work Order via the DMS system, entering basic information such as vehicle details, customer information, and fault descriptions. |
| 2       | 库存是否充足？           |               | After adding repair parts, the system automatically performs an inventory validation for the required parts to determine if inventory is sufficient. |
| 2a      | 提交工单                 |               | If inventory is sufficient, the SvA submits the Work Order.      |
| 2b      | 客户是否愿意等待？       |               | If inventory is insufficient, the system asks if the customer is willing to wait. If yes, the Work Order process continues. If no, the Work Order creation is terminated. |
| 3       | 工单是否涉及索赔？       |               | The system automatically determines if the Work Order involves a warranty claim. If yes, it enters the Claim Approval Process. If no, it proceeds directly to customer confirmation. |
| 4       | 索赔审批流程             |               | Work Orders involving warranty claims must go through the approval process. |
| 5       | 等待客户确认             |               | After approval is passed or a non-claim Work Order is submitted, the system pushes the details to the customer for confirmation. |
| 6       | 客户确认方式？           |               | The system supports both online and offline customer confirmation methods. |
| 6a      | 客户APP线上确认工单      |               | Customer views the Work Order details in the Super APP and confirms online. |
| 6b      | 上传客户的纸质确认表     |               | Customer signs the physical Work Order, and the SvA uploads a photo of the signed document. |
| 7       | 工单分配流程             |               | After customer confirmation, the Work Order enters the dispatching process to be assigned to the appropriate technician for repair. |
| 8       | 工单进行中               |               | The technician begins the repair work.                       |
| 9       | 维修中需要增项吗？       |               | During the repair process, it is determined whether additional repair items are needed. |
| 10      | 执行额外项目操作         |               | When additional items are required, the technician performs the add-item operation. |
| 11      | 增项客户确认方式？       |               | Additional items require customer confirmation, supported both online and offline. |
| 11a     | 客户APP确认增项          |               | Customer confirms the additional repair items and costs in the Super APP. |
| 11b     | 上传客户的纸质增项确认表 |               | Customer signs off on the additional items offline, and the SvA uploads the signed confirmation form. |
| 12      | 更新工单                 |               | After the customer confirms the additional items, the system merges the new items into the main Work Order, updating its content and cost. |
| 13      | 质量检查流程             |               | After the repair is completed, the process enters the quality control (QC) stage to ensure the repair quality meets standards. |
| 15      | 审批类型？               |               | The system supports two types of approvals for Work Orders: claim approval and cancellation approval. |
| 15a     | 判断是否质保期内         |               | For claim approval, the system checks if the vehicle is within the warranty period. If yes, it proceeds to first-level approval. If no, it is processed as a regular Work Order. |
| 15b     | 取消审批通过？           |               | For cancellation approval, the Service Manager determines whether to approve the request. |
| 15b1    | 工单状态恢复为取消前状态 |               | If the cancellation is not approved, the system automatically restores the Work Order to its previous status. |
| 15b2    | 工单状态变更为"已取消"   |               | If the cancellation is approved, the system changes the Work Order status to "Cancelled". |
| 16      | 索赔一级审批是否通过？   |               | The Foreman conducts the first-level review of the claim application and decides whether to approve it. |
| 16a1    | 工单审批驳回             |               | If the first-level approval is rejected, the Work Order's approval process is terminated. |
| 16a2    | 服务顾问编辑工单         |               | After claim rejection, the SvA removes the claim items and edits the Work Order content. |
| 16b1    | 推送二级审批任务         |               | After first-level approval, the system automatically pushes the approval task to HQ Staff. |
| 16b2    | 厂端管理人员进行二级审批 |               | HQ Staff conducts the final review.                        |
| 17      | 二级审批通过？           |               | HQ Staff conducts the final review. If approved, the Work Order proceeds to customer confirmation. If rejected, the approval process is terminated. |
| 18      | 工单客户确认流程         |               | The customer confirms the claim content.                     |

 

### **4.2.4 Dispatching Management**



|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps5.jpg) |

 



 

| **No.** | **Activity**   | **Documents** | **Description**                                            |
| ------- | -------------- | ------------- | ---------------------------------------------------------- |
| 1       | 查看派工仪表板 |               | Foreman views the dispatching dashboard to understand Work Order allocation and technician work status. |
| 2       | 工作派工       |               | Foreman dispatches the Work Order to a technician; the system updates the Work Order status and sends a notification. |
| 3       | 签到开始工作   |               | The technician checks in to start work on the Work Order; the system records the start time and updates the Work Order status. |
| 4       | 需要更换技师？ |               | The Foreman determines if a technician needs to be changed. If so, re-dispatch the work. |
| 5       | 需要暂停？     |               | The Foreman determines if the work needs to be paused.       |
| 5a1     | 暂停工作       |               | The Foreman pauses the current work, and the system records the pause time. |
| 5a2     | 恢复工作       |               | The Foreman resumes the paused work, and the system records the resume time. |
| 6       | 工作完成       |               | The technician completes the work; the Foreman records the completion time and updates the Work Order status. |

 

### **4.2.5 QC Management**



|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps6.jpg) |

 



 

 

 

 

| **No.** | **Activity** | **Documents** | **Description**                                            |
| ------- | ------------ | ------------- | ---------------------------------------------------------- |
| 1       | 创建质检单   | QC Form       | The system creates a QC Form, containing a checklist of QC items and basic information, to start the QC process. |
| 2       | 质检进行中   |               | The technician starts the quality control work.            |
| 3       | 填写质检表单 |               | The technician checks items against the QC checklist and fills in the results and related data in the system. |
| 4       | 提交质检结果 |               | After completing the QC form, the technician submits the results, and the system updates the status to "Pending Approval". |
| 5       | 审批结果判断 |               | The Foreman reviews the QC results and makes an approval decision (Pass/Fail). |
| 6a      | 进入结算流程 |               | If the QC is approved, the system updates the relevant status, and the process moves to the settlement stage. |
| 6b      | 执行返工     |               | If the QC fails, the technician performs rework and then re-fills the QC form. |

 

### **4.2.6 Settlement Management**



|      |                                                              |
| ---- | ------------------------------------------------------------ |
|      | ![img](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml18568/wps7.jpg) |

 



 

| **No.** | **Activity**      | **Documents** | **Description**                                              |
| ------- | ----------------- | ------------- | ------------------------------------------------------------ |
| 1       | 工单创建          |               | SvA creates a Work Order based on customer needs and vehicle maintenance items, providing data for the pre-settlement. |
| 2       | 生成预结算单      | Pre-settlement Bill | The system automatically generates a Pre-settlement Bill based on the Work Order, detailing service fees, parts costs, and labor charges. The amount is adjustable at this stage. |
| 3       | 质检完成          |               | The QC inspector completes the quality check, confirming the actual services rendered, providing an accurate basis for the final Settlement Bill. |
| 4       | 生成正式结算单    | Settlement Bill | After QC is complete, the system locks the settlement amount and generates a final Settlement Bill, confirming the final amount receivable. |
| 5       | 确认结算费用明细  |               | The customer reviews the detailed charges on the Settlement Bill in the Super APP, including service items, parts costs, and labor fees, and confirms them. |
| 6       | 金额比较结果判断  |               | The system automatically compares the settlement amount with the amount already paid to determine the next step. |
| 6a      | 执行退款操作      |               | When the settlement amount is less than the amount paid, the system initiates a refund process, calculates the refund amount, and prepares for the refund. |
| 6b      | 执行收款操作      |               | When the settlement amount is greater than the amount paid, the system initiates a payment collection process, calculates the amount due, and prepares for payment. |
| 7       | 支付方式选择      |               | The system processes the payment or refund based on the customer's chosen method (online/offline). |
| 7a      | 完成支付/退款     |               | The customer completes the online payment or receives the refund via the Super APP; the system receives the transaction result from the payment gateway. |
| 7b      | 处理线下支付/退款 | Transaction Record | Finance staff handles in-store cash or POS payments, or processes offline refunds, and records them in the system. |
| 8       | 更新支付/退款信息 |               | The system receives the payment or refund result and updates the Settlement Bill status and the amount paid to ensure data synchronization. |
| 9       | 接收退款信息      |               | The finance system receives the refund information from DMS for accounting and voucher management. |
| 10      | 填写服务反馈      | Service Feedback Record | After payment, the customer provides service feedback and ratings through the Super APP, offering input on service quality. |
| 11      | 更新反馈信息      |               | The system receives customer feedback, updates the service record, and provides data for service quality improvement. |

 

 