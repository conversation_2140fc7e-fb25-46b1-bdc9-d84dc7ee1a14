# AI辅助draw.io流程图翻译及词汇库管理规则 (v2.1)

本规则旨在建立一个高效、可迭代的流程，用于将中文`.drawio.xml`流程图翻译成英文，并在过程中持续构建和维护一个统一的、权威的专业名词词汇库。

---

## 核心原则

*   **单一来源 (Single Source of Truth):** `master-glossary.md` 文件是所有专业术语中英翻译的唯一权威来源。如果该文件不存在，则以本规则文件内的词汇库为准。
*   **增量更新 (Incremental Update):** 在翻译新文件时，逐步发现并确认新词汇。
*   **先确认，后定稿 (Confirm First, Then Finalize):** 必须先与用户确认新词汇的翻译，然后才能生成最终的英文版本并更新词汇库。
*   **人机协作 (Human-AI Collaboration):** AI负责执行繁琐的翻译和替换工作，人类负责最终的审查和确认。

---

## 工作流程

本流程分为两大模式：**A. 翻译新流程图（被动模式）** 和 **B. 批量维护词汇（主动模式）**。

### A. 翻译新流程图（被动模式）

此模式用于处理新的、尚未翻译的流程图文件。

**第一步：用户发起请求**
*   **用户:** "请帮我翻译 `[文件路径]/[文件名].drawio.xml`。"

**第二步：AI执行初步翻译与新词识别**
1.  **复制Sheet页:** AI打开指定的 `.drawio.xml` 文件，在文件内部复制需要翻译的中文Sheet页（Diagram），并将其重命名为 `[原Sheet页名]_en`。**注意：不是创建新文件。**
2.  **查阅主词汇库:** AI读取 `dms/AI-流程图/master-glossary.md` (如果存在) 或本规则文件内的词汇库内容。
3.  **执行初步翻译:** 在新创建的 `_en` Sheet页上进行操作。
    *   对于在主词汇库中能找到的中文术语，AI严格按照库中对应的英文进行翻译。
    *   对于未在主词汇库中找到的中文术语，AI进行初步翻译，并在生成的文本旁用 `*` 或 `(待确认)` 等方式进行标记。
4.  **生成待确认列表:** AI完成初步翻译后，会整理出所有本次遇到的 **新词汇**，并向用户呈现一个清晰的"新词汇确认列表"。
    *   **示例输出:**
        > "已完成初步翻译。在翻译过程中，我遇到了以下新词汇，请您确认：
        > - `厂端` -> `DMS-HQ` (建议)
        > - `撞库` -> `Data Collision` (建议)
        >
        > 请问以上翻译是否准确？您可以直接批准或提供修正意见。文件内的英文Sheet页也已创建，但部分词汇待您确认后才能最终定稿。"

**第三步：用户审核与确认**
*   **用户:** "第一个确认，第二个修改为 `Check for Duplicates`。"

**第四步：AI定稿与归档**
1.  **最终化翻译:** AI根据用户的反馈，**回到刚才修改的 `_en` Sheet页**，将所有待确认的词汇更新为最终版本。
2.  **更新主词汇库:** AI将用户确认过的新词汇（包括修正后的）追加到 `dms/AI-流程图/master-glossary.md` (如果存在) 或本规则文件的词汇库中，并保持字母排序。
3.  **完成报告:** AI向用户报告任务完成，并指明词汇库已同步更新。

### B. 批量维护词汇（主动模式）

此模式用于当用户希望对一个或多个词汇在 **所有历史文件** 中进行统一修改时。

**第一步：用户发起批量修改指令**
*   **用户:** "请将所有流程图中的 `SA` 修改为 `Sales Advisor`，并将 `门店` 的翻译从 `Store` 改为 `Outlet`。"

**第二步：AI更新主词汇库**
1.  **定位与修改:** AI首先打开 `dms/AI-流程图/master-glossary.md` (如果存在) 或本规则文件的词汇库。
2.  **更新记录:** 根据用户的指令，修改或添加相应的词条。例如，将 `| SA | 销售顾问 |` 修改为 `| Sales Advisor | 销售顾问 |`，并将 `| Store | 门店 |` 修改为 `| Outlet | 门店 |`。
3.  **报告词汇库更新:** AI告知用户主词汇库已更新。

**第三步：AI批量更新所有流程图文件**
1.  **扫描文件:** AI会扫描 `dms/AI-流程图/` 及其所有子目录下的 `*_en.drawio.xml` 或包含英文Sheet页的 `.drawio.xml` 文件。
2.  **执行替换:** 对于每一个文件，AI会根据用户的指令执行"查找与替换"操作。例如，在XML内容中，将 `value="SA"` 替换为 `value="Sales Advisor"`，将 `value="门店"` (在英文文件中) 或 `value="Store"` 替换为 `value="Outlet"`。
3.  **完成报告:** AI向用户提供一份本次批量操作所修改的所有文件的清单。
    *   **示例输出:**
        > "主词汇库已更新。并已根据您的指令，完成了对以下文件的批量修改：
        > - `dms/AI-流程图/销售/潜客管理/潜客跟进-布局优化版.drawio.xml` (在 `跟进_en` Sheet页中)
        > - `dms/AI-流程图/销售/到店试驾/到店试驾-流程图-V1.0.drawio.xml`
        > - ... (其他受影响的文件)
        >
        > 所有历史流程图均已与最新的词汇标准保持一致。"

---

## 主词汇库 (`master-glossary.md`) 格式规范

*   **位置:** `dms/AI-流程图/master-glossary.md` (优先使用)
*   **格式:** Markdown表格
*   **字段:**
    *   `English Term`: 英文术语（主键）
    *   `Chinese Term`: 中文术语
*   **排序:** 表格内容需始终按 `English Term` 字段的字母顺序升序排列。

**示例:**
```markdown
| English Term | Chinese Term |
|---|---|
| DMS-HQ | 厂端 |
| DMS-Outlet | 店端 |
| Outlet | 门店 |
| SA | 销售顾问 |
| SUPER APP | 超级应用 |
```

## 4. 专业名词词汇库 (Master Glossary)

本词汇库是所有流程图翻译的**权威参考**。AI在进行翻译时，**必须**优先使用本库中定义的译文。所有新识别的专业名词在经您确认后，将统一添加到此库中进行维护。

### 角色/系统 (Roles/Systems)

| 中文 | 英文 |
| :--- | :--- |
| DMS-厂端 | DMS-HQ |
| DMS-店端 | DMS-Outlet |
| DMS系统 | DMS System |
| SUPER APP端 (客户) | SUPER APP (Customer) |
| 销售顾问 | SA |
| 客户 | Customer |
| 财务人员 | Finance Staff |
| 保险系统 | Insurance Management System |
| JPJ车辆登记平台 | JPJ Registration System|
| JPJ注册系统 | JPJ Registration System |
| 销售经理 | Sales Manager |
| 区域经理 | Regional Manager |
| HQ配车专员 | HQ Allocation Specialist |
| 车辆登记人员 | Registration Staff |
| ERP系统 | ERP System |
| 邮件系统 | Email System |
| 服务顾问 | Service Advisor |
| 技师 | Technician |
| 技师经理 | Technician Manager |
| 服务经理 | Service Manager |
| 质检员 | QC Inspector |
| 仓库管理员 | Warehouse Manager |
| 店长 | Store Manager |
| 门店经理 | Store Manager |
| 厂端管理人员 | HQ Management Staff |
| 厂端审批人员 | HQ Approval Staff |
| 门店用户 | Store User |
| 门店库存管理员 | Store Inventory Manager |
| 系统管理员 | System Administrator |
| 配车专员 | Allocation Specialist |

### 核心概念/对象 (Core Concepts/Objects)

| 中文 | 英文 |
| :--- | :--- |
| 待登记 | Awaiting Registration |
| 意向级别 (H/A/B/C) | Intention Level (H/A/B/C) |
| 保单信息 | Policy Info |
| 保险方案 | Insurance Plan |
| 前置条件 | Prerequisite |
| 分期付款 | Loan |
| 全款 | Cash |
| 订单 | Order |
| 订单取消 | Order Cancelled |
| 客户档案 | Customer File |
| 手机号 | Phone Number |
| 定金 | Deposit |
| 已完成配车 | Vehicle Allocated |
| 车型 | Vehicle Model |
| 门店及顾问 | Outlet & SA |
| 线下收退款 | Offline Payment/Refund |
| OTR费用结构 | OTR Fee Structure |
| 潜客 | Prospect |
| 潜客池 | Prospect Pool |
| 潜客档案 | Prospect File |
| 收退款 | Payment/Refund |
| 试驾登记单 | Test Drive Registration Form |
| 投保人 | Policyholder |
| 车辆VIN号 | VIN |
| 车辆已投保 | Vehicle Insured |
| 登记信息及费用 | Registration Info & Fees |
| 登记证书号 | Registration Certificate No. |
| 待配车订单 | Orders to be Allocated |
| 可配车辆 | Allocatable Vehicles |
| 配车记录 | Allocation Record |
| 订单配车状态 | Order Allocation Status |
| 车辆状态 | Vehicle Status |
| 车辆资源 | Vehicle Resources |
| 门店入库 | Outlet Inbound |
| 车辆数据生成 | Vehicle Data Generation |
| 服务预约 | Service Appointment |
| 预约单 | Appointment Order |
| 预约限量 | Appointment Quota |
| 预约看板 | Appointment Dashboard |
| 到店登记 | Store Check-in Registration |
| 登记单 | Registration Form |
| 环检单 | Inspection Form |
| 电子环检 | Electronic Inspection |
| 工单 | Work Order |
| 维修工单 | Repair Work Order |
| 保养工单 | Maintenance Work Order |
| 索赔工单 | Warranty Claim Work Order |
| 工单增项 | Work Order Additional Items |
| 派工单 | Work Assignment Order |
| 质检单 | Quality Check Form |
| 结算单 | Settlement Bill |
| 叫料清单 | Parts Request List |
| 拣货单 | Picking List |
| 报损单 | Damage Report |
| 零件库存 | Parts Inventory |
| 零件报损 | Parts Damage |
| 保养套餐 | Maintenance Package |
| 服务类型 | Service Type |
| 车辆信息 | Vehicle Information |
| 送修人 | Service Requester |
| 车牌号 | License Plate Number |
| 进场里程 | Entry Mileage |
| 工时 | Labor Hours |
| 零件 | Parts |
| 费用明细 | Fee Details |
| 支付状态 | Payment Status |
| 优惠券 | Coupon |
| 收款凭证 | Payment Receipt |
| 退款凭证 | Refund Receipt |

### 主要动作/流程 (Key Actions/Processes)

| 中文 | 英文 |
| :--- | :--- |
| 注册super APP | Register SUPER APP |
| 手动创建潜客档案 | Manually Create Prospect File |
| 查询潜客信息 | Find/Query Prospect Info |
| 关联（销售顾问） | Associate (with a consultant) |
| 编辑潜客档案 | Edit Prospect File |
| 潜客跟进 | Prospect Follow-up |
| 更新意向级别 | Update Intention Level |
| 试驾管理 | Test Drive Management |
| 安排试驾 | Arrange Test Drive |
| 创建订单 | Create Order |
| 支付定金 | Pay Deposit |
| 同步信息 | Synchronize Information |
| 更新潜客状态 | Update Prospect Status |
| 到店试驾 | In-Store Test Drive |
| 查找客户档案 | Find Customer File |
| 创建试驾登记单 | Create Test Drive Registration Form |
| 记录试驾情况 | Record details of this test drive |
| 更新试驾记录 | Update Test Drive Record |
| 查询并归档数据 | Query and archive data |
| 统计分析 | statistical analysis |
| **客户:** 提交订单创建请求 | **Customer:** Submits order creation request |
| **客户:** 选择车型配置、付款方式、门店和销售顾问 | **Customer:** Selects model, payment, outlet, SA |
| **系统:** 生成订单信息 | **System:** Generates order information |
| 定金支付完成 | Deposit paid |
| **客户:** 提交修改申请 | **Customer:** Submits modification request |
| **销售顾问:** 审核通过 | **SA:** Approves request |
| **销售顾问:** 提交修改订单申请并说明原因 | **SA:** Submits modification request with reason |
| **销售顾问:** 提交取消订单申请并说明原因 | **SA:** Submits cancellation request with reason |
| 整车收退款管理 | Vehicle Payment & Refund Mgt. |
| 确认收款 | Confirm payment |
| 计算退款金额 | Calculate refund amount |
| 退款完成 | Refund completed |
| 客户付款方式选择 | Select payment method |
| **销售顾问:** 帮助客户线下向银行申请贷款 | **SA:** Helps customer apply for bank loan (offline) |
| 配车管理 | Vehicle Allocation Mgt. |
| **HQ配车专员:** 配车 | **HQ Allocation Specialist:** Allocates vehicle |
| **销售顾问:** 校验交车前置条件 | **SA:** Verifies pre-delivery conditions |
| 交车管理 | Vehicle Delivery Mgt. |
| **销售顾问:** 提交交车 | **SA:** Submits for delivery |
| **客户:** 确认已交车 | **Customer:** Confirms delivery |
| 推送订单信息至保险系统 | Push order info to Insurance Mgt. System |
| 车辆保险管理 | Vehicle Insurance Mgt. |
| **销售顾问:** 为客户投保 | **SA:** Arranges insurance for customer |
| **保险系统:** 回传保单信息 | **Insurance Mgt. System:** Sends back policy info |
| 推送投保信息及订单信息至JPJ注册系统 | Push info to JPJ System |
| 车辆登记管理 | Vehicle Registration Mgt. |
| **销售顾问:** 为客户在JPJ系统上注册车辆信息 | **SA:** Registers vehicle in JPJ System for customer |
| **JPJ系统:** 回传登记信息及费用 | **JPJ System:** Sends back registration info & fees |
| 保证处理 | Insurance Processing |
| 保险失败 | Insurance Failure |
| 保险中 | Insurance in Progress |
| 校对 | Verify |
| 确认车辆入库 | Confirm Vehicle Inbound |
| 登记处理 | Registration Processing |
| 登记请求 | Registration Request |
| 登记结果 | Registration Result |
| 重新推送 | Resubmit |
| 配车专员 | Allocation Specialist |
| 工厂订单号 | Factory Order Number |
| 已配车 | Allocated |
| 未配车 | Not Allocated |
| 已锁定 | Locked |
| 未锁定 | Unlocked |
| 取消配车 | Cancel Allocation |
| 重新配车 | Reallocate |
| 同步数据 | Sync Data |
| 配车流程结束 | End of Allocation Process |
| 服务预约管理 | Service Appointment Management |
| 查看预约列表 | View Appointment List |
| 创建预约 | Create Appointment |
| 编辑预约 | Edit Appointment |
| 取消预约 | Cancel Appointment |
| 确认预约 | Confirm Appointment |
| 核销预约单 | Verify Appointment Order |
| 客户到店 | Customer Arrival |
| 到店登记 | Store Check-in Registration |
| 录入车辆信息 | Enter Vehicle Information |
| 分配技师 | Assign Technician |
| 执行电子环检 | Perform Electronic Inspection |
| 填写环检单 | Fill Inspection Form |
| 拍照记录损伤 | Photo Damage Records |
| 生成带二维码环检单 | Generate QR Code Inspection Form |
| 客户确认环检单 | Customer Confirms Inspection Form |
| 创建工单 | Create Work Order |
| 填写工单信息 | Fill Work Order Information |
| 编辑工单 | Edit Work Order |
| 保存工单草稿 | Save Work Order Draft |
| 提交工单确认 | Submit Work Order Confirmation |
| 引导客户确认工单 | Guide Customer to Confirm Work Order |
| 提交索赔审核 | Submit Warranty Claim Review |
| 跟踪索赔审批进度 | Track Warranty Claim Approval Progress |
| 处理审批结果 | Handle Approval Results |
| 处理工单增项 | Handle Work Order Additional Items |
| 直接取消工单 | Direct Cancel Work Order |
| 提交工单取消申请 | Submit Work Order Cancellation Request |
| 打印工单 | Print Work Order |
| 导出工单数据 | Export Work Order Data |
| 审批工单取消申请 | Approve Work Order Cancellation Request |
| 处理索赔一级审批 | Handle First-level Warranty Claim Approval |
| 处理索赔二级审批 | Handle Second-level Warranty Claim Approval |
| 监控审批时效 | Monitor Approval Timeliness |
| 查看审批统计分析 | View Approval Statistics Analysis |
| 导出索赔审批数据 | Export Warranty Claim Approval Data |
| 派工管理 | Work Assignment Management |
| 查看派工看板 | View Work Assignment Dashboard |
| 查看技师时间轴 | View Technician Timeline |
| 监控技师工作状态 | Monitor Technician Work Status |
| 分配工单 | Assign Work Order |
| 选择技师 | Select Technician |
| 设置预计时间 | Set Estimated Time |
| 重新分配工单 | Reassign Work Order |
| 暂停/恢复工单 | Pause/Resume Work Order |
| 确认完工 | Confirm Completion |
| 导出派工数据 | Export Work Assignment Data |
| 查看个人任务列表 | View Personal Task List |
| 接收派工通知 | Receive Work Assignment Notification |
| 确认开工 | Confirm Start Work |
| 执行维修保养工作 | Perform Repair Maintenance Work |
| 质检管理 | Quality Check Management |
| 查看个人质检任务列表 | View Personal QC Task List |
| 筛选查询质检单 | Filter Query QC Forms |
| 开始自检并填写结果 | Start Self-inspection and Fill Results |
| 提交自检结果 | Submit Self-inspection Results |
| 查看并执行返工任务 | View and Execute Rework Tasks |
| 提交返工结果 | Submit Rework Results |
| 查看质检单详情与日志 | View QC Form Details and Logs |
| 导出个人质检数据 | Export Personal QC Data |
| 查看待审批质检单列表 | View Pending QC Approval List |
| 审核质检单 | Review QC Form |
| 确认质检通过 | Confirm QC Pass |
| 发起返工 | Initiate Rework |
| 确认返工提交 | Confirm Rework Submission |
| 查看已审批质检单 | View Approved QC Forms |
| 导出质检管理数据 | Export QC Management Data |
| 结算管理 | Settlement Management |
| 查看结算单列表 | View Settlement List |
| 筛选查询结算单 | Filter Query Settlement Bills |
| 查看结算单详情 | View Settlement Details |
| 打印结算单 | Print Settlement Bill |
| 监控工单关联的结算单生成 | Monitor Work Order Related Settlement Generation |
| 在工单中调整服务项目 | Adjust Service Items in Work Order |
| 编辑结算单优惠信息 | Edit Settlement Discount Information |
| 监控质检完成触发的金额锁定 | Monitor Amount Lock Triggered by QC Completion |
| 推送结算单给客户 | Push Settlement Bill to Customer |
| 处理客户费用咨询与异议 | Handle Customer Fee Inquiry and Disputes |
| 跟踪客户支付状态 | Track Customer Payment Status |
| 确认收款完成 | Confirm Payment Completion |
| 监控退款处理 | Monitor Refund Processing |
| 通知客户提车 | Notify Customer for Vehicle Pickup |
| 完成车辆交接 | Complete Vehicle Handover |
| 导出结算数据 | Export Settlement Data |
| 查看待收款结算单列表 | View Pending Payment Settlement List |
| 查看门店所有结算单 | View All Store Settlement Bills |
| 接待到店付款客户 | Receive In-store Payment Customers |
| 完成并录入线下收款 | Complete and Record Offline Payment |
| 处理组合支付 | Handle Combined Payment |
| 处理线下退款任务 | Handle Offline Refund Tasks |
| 处理优惠券录入 | Handle Coupon Entry |
| 上传收退款凭证 | Upload Payment/Refund Receipts |
| 执行日结对账 | Execute Daily Reconciliation |
| 导出收退款数据 | Export Payment/Refund Data |
| 查看门店所有结算数据 | View All Store Settlement Data |
| 监控与分析结算数据 | Monitor and Analyze Settlement Data |
| 处理结算争议与异常 | Handle Settlement Disputes and Exceptions |
| 导出管理报表 | Export Management Reports |
| 零件管理 | Parts Management |
| 查看零件库存信息 | View Parts Inventory Information |
| 搜索零件信息 | Search Parts Information |
| 新建叫料清单 | Create Parts Request List |
| 填写叫料清单详情 | Fill Parts Request List Details |
| 提交叫料清单 | Submit Parts Request List |
| 查看叫料清单列表 | View Parts Request List |
| 审核叫料清单 | Review Parts Request List |
| 生成拣货单 | Generate Picking List |
| 查看拣货单列表 | View Picking List |
| 查看拣货单详情 | View Picking List Details |
| 执行拣货操作 | Execute Picking Operation |
| 完成拣货单 | Complete Picking List |
| 记录零件报损 | Record Parts Damage |
| 填写报损原因 | Fill Damage Reason |
| 上传报损凭证 | Upload Damage Evidence |
| 提交报损申请 | Submit Damage Report |
| 查看报损申请列表 | View Damage Report List |
| 审核报损申请 | Review Damage Report |
| 批准报损申请 | Approve Damage Report |
| 驳回报损申请 | Reject Damage Report |
| 查看报损记录列表 | View Damage Record List |
| 筛选报损记录 | Filter Damage Records |
| 查看报损记录详情 | View Damage Record Details |
| 分析报损趋势 | Analyze Damage Trends |
| 导出叫料清单 | Export Parts Request List |
| 导出拣货单 | Export Picking List |
| 导出报损记录 | Export Damage Records |
| 打印拣货单 | Print Picking List |
| 发现零件破损 | Discover Parts Damage |
| 新建报损单 | Create Damage Report |
| 审批报损单 | Approve Damage Report |
| 查看审批结果 | View Approval Results |
| 修改或重做报损单 | Modify or Redo Damage Report |
| 库存标记破损件 | Mark Damaged Items in Inventory |
| 接收数据信息 | Receive Data Information |
| 监控流程结束 | Monitor Process End |

### 状态/决策 (Statuses/Decisions)

| 中文 | 英文 |
| :--- | :--- |
| 有意向 | Intention|
| 暂不明确 | Uncertain |
| 明确无购买意向 | No intention|
| 战败 | Lost (Sales Term) |
| 订单预订成功 | Order Reservation Successful |
| 订单流程结束 | End |
| 开始 | Start |
| 成功 / 失败 | Success / Failed |
| 客户是否在72小时内支付定金? | Pay deposit within 72h? |
| 客户是否修改个人信息申请？ | Request to modify personal info? |
| 客户是否修改车辆颜色？ | Request to change vehicle color? |
| 客户是否取消订单？ | Request to cancel order? |
| 销售经理审核是否通过？ | Sales Manager approval? |
| 区域经理审核是否通过？ | Regional Manager approval? |
| 尾款是否结清 | Final payment settled? |
| 贷款是否通过？ | Loan approved? |
| 是否可以交车？ | Ready for delivery? |
| 保险投保结果? | Insurance successful? |
| 车辆登记结果? | Registration successful? |
| 是否预约用户 | Appointment User? |
| 服务类型 | Service Type? |
| 套餐推荐 | Package Recommended? |
| 质检结果 | QC Result? |
| 工单类型判断 | Work Order Type Judgment? |
| 是否有增项 | Has Additional Items? |
| 草稿 | Draft |
| 待确认 | Pending Confirmation |
| 已确认 | Confirmed |
| 待分配 | Pending Assignment |
| 待开工 | Pending Start |
| 进行中 | In Progress |
| 待质检 | Pending QC |
| 质检通过 | QC Passed |
| 已完成 | Completed |
| 已取消 | Cancelled |
| 返工 | Rework |
| 待环检 | Pending Inspection |
| 环检中 | Under Inspection |
| 已确认 | Confirmed |
| 正常 | Normal |
| 已取消 | Cancelled |
| 核算中 | Under Calculation |
| 待支付 | Pending Payment |
| 支付中 | Payment in Progress |
| 已结清 | Settled |
| 退款中 | Refund in Progress |
| 待审批 | Pending Approval |
| 已审批 | Approved |
| 已驳回 | Rejected |
| 空闲 | Idle |
| 工作中 | Working |
| 请假 | On Leave |
| 良好 | Good |
| 需要注意 | Needs Attention |
| 不良 | Poor |
| 不适用 | Not Applicable |
| 合格 | Qualified |
| 不合格 | Unqualified |
| 通过 | Pass |
| 不通过 | Fail |
| 未到店 | Not Arrived |
| 已到店 | Arrived |
| 未履约 | Not Fulfilled |

---

## 协作流程回顾与优化 (v2.1更新)

本章节旨在记录和反思协作过程中发现的问题，以持续优化工作流程。

1.  **问题：执行顺序与规则不符**
    *   **现象：** 在v2.0规则执行中，AI先生成了最终的英文翻译文件，之后才请求用户确认新词汇并更新词库。
    *   **根本原因：** AI的执行逻辑倾向于完成独立的闭环任务，而未能将"用户确认"这一关键步骤正确地嵌入到"翻译初稿 -> 确认 -> 定稿"的流程中。
    *   **优化措施 (v2.1已实施)：** 明确规定了"先确认，后定稿"的原则。AI必须先产出包含待确认词汇的**初步翻译版本**，待用户确认后，才能使用标准化词汇完成最终翻译，并同步更新词汇库。

2.  **问题：输出格式与最佳实践不符**
    *   **现象：** AI创建了全新的 `_en.drawio.xml` 文件，而用户期望的是在原文件内创建新的英文Sheet页以便于管理和对照。
    *   **根本原因：** v2.0的规则文档明确指示"复制文件"，AI严格遵守了该成文规则，但这与用户心中的最佳实践存在偏差。这暴露了"规则文档"与"理想流程"的脱节。
    *   **优化措施 (v2.1已实施)：** 明确规定AI的输出格式为"在原文件内创建并命名新的英文Sheet页"，使规则与最佳实践保持一致。 