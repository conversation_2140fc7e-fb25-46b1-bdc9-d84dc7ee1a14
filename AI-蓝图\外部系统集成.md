*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                           |
| ------------ | ------------------------- | ------------------------------------------------------------ |
| 1            | Outlet Info                  | DMS provides basic outlet information to SUPER APP for customer inquiry and selection. |
| 2            | SA Info              | DMS provides detailed SA information to SUPER APP, making it convenient for customers to choose a suitable advisor. |
| 3            | Vehicle Model Info              | DMS syncs vehicle model info to SUPER APP, including specifications, configuration parameters, etc. |
| 4            | Vehicle Price Info              | DMS provides real-time vehicle price info to SUPER APP, including base price and optional accessory prices. |
| 5            | Basic Data Display              | SUPER APP receives and displays the outlet, SA, vehicle model, and price info pushed by DMS. |
| 6            | Order Creation/Editing             | Customer selects vehicle model configuration, outlet, and SA in SUPER APP to create or edit an order. |
| 7            | Order Info Sync              | DMS receives order info from SUPER APP, including vehicle model, payment method, etc. |
| 8            | Booking Fee Calculation              | DMS calculates and provides the order's booking fee (deposit) information to SUPER APP. |
| 9            | Online Payment Processing              | Customer completes the online payment for the deposit or other fees in SUPER APP. |
| 10           | Payment Result Reception              | DMS receives payment result information from SUPER APP and updates the order's payment status. |
| 11           | Sync Payment Status              | SUPER APP syncs payment processing results to DMS to ensure status consistency on both ends. |
| 12           | Provide Order Details              | DMS provides customer's detailed order information to SUPER APP, including status, configuration, price, etc. |
| 13           | Order Status Display              | SUPER APP receives and displays the latest order status, allowing customers to track order progress. |
| 14           | Personal Info Change              | Customer modifies personal info in SUPER APP and submits a change request to DMS for review. |
| 15           | Inquiry Request Submission              | Customer initiates various business inquiry requests to the SA through SUPER APP. |

 

 

 

 

**1.1.1** ***\*Integration of DMS and ERP\****

*********** ***\*Integration Description\****

Integration of DMS and ERP includes vehicle model master data, vehicle data, sales order data, payment/refund information, delivery data, and invoice data.

*********** 

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                   |
| ------------ | ------------------------- | ---------------------------------------------------- |
| 1            | Vehicle Model Master Data | ERP syncs vehicle model master data to DMS to provide basic information for vehicle management. |
| 2            | Vehicle Info       | ERP syncs vehicle info (VIN) to DMS for vehicle identification and management. |
| 3            | Vehicle Info       | After receiving vehicle info, outlet staff performs stock-in operation in DMS. |
| 4            | Inventory Info     | DMS sends stock-in info back to ERP to maintain inventory data synchronization. |
| 5            | Order Info         | After an order is created in DMS, it syncs the order info to ERP for order management. |
| 6            | Payment/Refund Info       | DMS sends payment/refund info to ERP to sync financial status. |
| 7            | Delivery Order/Delivery Info            | DMS syncs order delivery info to ERP to complete the vehicle stock-out. |
| 8            | Invoice Info       | ERP syncs invoice info to DMS to complete the invoice management process. |

 

**1.1.2** ***\*Integration of DMS and VIPRO\****

*********** ***\*Integration Description\****

Integration of DMS and VIPRO includes insurance application information.

*********** 

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                           |
| ------------ | ------------------------- | ------------------------------------------------------------ |
| 1            | Insurance Application     | DMS initiates a vehicle insurance application, containing customer's personal and vehicle information. |
| 2            | Insurance Result          | VIPRO System returns the vehicle insurance result, including the insurance status. DMS receives the result and updates the relevant business status. |

 

**1.1.3** ***\*Integration of DMS and JPJ\****

*********** ***\*Integration Description\****

Integration of DMS and JPJ includes vehicle registration information.

*********** 

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\****    | ***\*Activity\****                            |
| ------------ | ---------------------------- | --------------------------------------------- |
| 1            | Vehicle Registration Request | DMS initiates a vehicle registration request, containing vehicle and owner related information. |
| 2            | Registration Result          | JPJ returns the vehicle registration result, including registration status and related information. |

 

1.1 ***\*Service Module External Integration\****

**1.1.4** ***\*Integration of DMS and Super APP\****

*********** ***\*Integration Description\****

Integration of DMS and Super APP includes basic data, service appointment data, maintenance packages, appointment cancellations, and appointment payment data.

*********** 

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                                           |
| ------------ | ------------------------- | ------------------------------------------------------------ |
| 1            | Outlet Info         | DMS provides outlet info to SUPER APP for customer inquiry and selection. |
| 2            | Customer Vehicle History  | DMS provides customer vehicle history to SUPER APP for service recommendations. |
| 3            | Available Time Slots      | DMS provides outlet's available time slots to SUPER APP to display bookable times. |
| 4            | Maintenance Package       | DMS provides maintenance package and price information to SUPER APP for customers to view and select. |
| 5            | Info Display       | SUPER APP receives and displays the outlet, vehicle history, time slot, and package info pushed by DMS. |
| 6            | Make an Appointment      | Customer places/edits an order in SUPER APP to create appointment info. |
| 7            | Appointment Info   | DMS receives appointment creation info from SUPER APP and processes the appointment request. |
| 8            | Package Price Info | DMS provides the price information of the selected package to SUPER APP for payment confirmation. |
| 9            | Online Payment            | Customer completes the online payment in SUPER APP.          |
| 10           | Payment/Refund Info       | DMS receives payment info from SUPER APP to confirm payment status. |
| 11           | Sync Payment info              | SUPER APP syncs payment info to DMS to maintain payment status consistency. |
| 12           | Appointment List          | DMS provides customer's appointment list to SUPER APP to display appointment records. |
| 13           | List Display              | SUPER APP receives and displays the customer's appointment list. |
| 14           | Appointment Details       | DMS provides customer's appointment details to SUPER APP to display detailed content. |
| 15           | Details Display           | SUPER APP receives and displays the customer's appointment details. |
| 16           | Cancellation Request      | Customer cancels an appointment in SUPER APP and submits a cancellation request. |
| 17           | Cancellation Information  | DMS receives cancellation info from SUPER APP and processes the cancellation request. |
| 18           | Refund Process            | If a refund is needed, SUPER APP syncs the refund info to DMS for processing. |
| 19           | Refund Info        | DMS receives refund info from SUPER APP and processes the refund. |

 

 

 

**1.1.5** ***\*Integration of DMS and ERP\****

*********** ***\*Integration Description\****

Integration of DMS and ERP includes service work order payments, refund information, parts requisition data, stock-in information, and parts usage data.

*********** 

***\*Integration Flowchart\****



 

*********** ***\*Integration Flow Description\****

| ***\*SN\**** | ***\*Data/ Documents\**** | ***\*Activity\****                          |
| ------------ | ------------------------- | ------------------------------------------- |
| 1            | Payment Collection        | DMS processes online and offline payment/refund operations. |
| 2            | Payment/Refund Info       | DMS sends payment/refund info to ERP to sync financial status. |
| 3            | Payment/Refund Info       | ERP receives payment/refund info and updates financial records. |
| 4            | Transfer Order            | ERP generates a transfer order to handle related financial flows. |
| 5            | Parts Requisition             | DMS creates a parts requisition to request necessary parts from ERP. |
| 6            | Inventory Update          | DMS receives goods and syncs inventory change information to ERP. |
| 7            | Inventory Info     | ERP receives outlet's parts inventory change information and updates inventory status. |
| 8            | Parts Usage               | DMS syncs parts usage information from work orders to ERP. |

 