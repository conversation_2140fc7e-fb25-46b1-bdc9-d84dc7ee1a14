<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0">
  <diagram name="English Version" id="en_dispatch_flow">
    <mxGraphModel dx="1345" dy="608" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="en_0" />
        <mxCell id="en_1" parent="en_0" />
        <mxCell id="en_swimlane_1" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fontSize=16;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="en_1" vertex="1">
          <mxGeometry x="23" y="32" width="782" height="1014" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_18" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="355" y="917" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="aDIvWjzlG2wTKlU-VJo_-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="en_cell_27" target="CSz7zE2x3lNiazQoJA17-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="524" y="554" />
              <mxPoint x="524" y="310" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="aDIvWjzlG2wTKlU-VJo_-2" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="aDIvWjzlG2wTKlU-VJo_-1" vertex="1" connectable="0">
          <mxGeometry x="-0.8397" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDIvWjzlG2wTKlU-VJo_-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="en_cell_27" target="aDIvWjzlG2wTKlU-VJo_-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aDIvWjzlG2wTKlU-VJo_-5" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="aDIvWjzlG2wTKlU-VJo_-4" vertex="1" connectable="0">
          <mxGeometry x="-0.4418" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="en_cell_27" value="4.Need to change the technician?" style="rhombus;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="315" y="514" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CSz7zE2x3lNiazQoJA17-0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="en_cell_34" target="en_cell_40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_34" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="355" y="77" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="CSz7zE2x3lNiazQoJA17-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="en_swimlane_1" source="en_cell_40" target="CSz7zE2x3lNiazQoJA17-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="en_cell_40" value="1. View job dispatch dashboard" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="328.5" y="171" width="133" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CSz7zE2x3lNiazQoJA17-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="CSz7zE2x3lNiazQoJA17-1" target="CSz7zE2x3lNiazQoJA17-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CSz7zE2x3lNiazQoJA17-1" value="2. Job dispatch" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="328.5" y="280" width="133" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CSz7zE2x3lNiazQoJA17-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="CSz7zE2x3lNiazQoJA17-3" target="en_cell_27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CSz7zE2x3lNiazQoJA17-3" value="3. Check in to start work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="323" y="397" width="144" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="aDIvWjzlG2wTKlU-VJo_-3" target="6zaFEiIAo7rl7y9OpZte-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-9" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6zaFEiIAo7rl7y9OpZte-3" vertex="1" connectable="0">
          <mxGeometry x="-0.3472" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="aDIvWjzlG2wTKlU-VJo_-3" target="6zaFEiIAo7rl7y9OpZte-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-8" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6zaFEiIAo7rl7y9OpZte-5" vertex="1" connectable="0">
          <mxGeometry x="-0.5594" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDIvWjzlG2wTKlU-VJo_-3" value="5.Need to pause?" style="rhombus;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="315" y="652" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="6zaFEiIAo7rl7y9OpZte-1" target="en_cell_18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-1" value="6. Work is completed" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="275" y="797" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="en_swimlane_1" source="6zaFEiIAo7rl7y9OpZte-4" target="CSz7zE2x3lNiazQoJA17-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="151" y="427" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6zaFEiIAo7rl7y9OpZte-4" value="5a. Pause work" style="rounded=1;whiteSpace=wrap;html=1;" parent="en_swimlane_1" vertex="1">
          <mxGeometry x="61" y="526" width="180" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
