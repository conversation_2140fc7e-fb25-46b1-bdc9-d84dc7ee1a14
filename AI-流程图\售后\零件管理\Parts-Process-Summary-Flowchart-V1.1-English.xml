<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="Parts Process Summary Flowchart-V1.1 English" id="parts-process-flow-v11-en">
    <mxGraphModel dx="2865" dy="2328" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="500" pageHeight="1000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="inzhINmpMBKd6LQynHdK-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;strokeColor=none;" parent="1" source="swimlane1" target="swimlane3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="swimlane1" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-370" y="-515" width="750" height="1850" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-21" value="" style="endArrow=classic;html=1;rounded=0;" parent="swimlane1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="233" y="1180.8" as="sourcePoint" />
            <mxPoint x="352" y="1180.1703703703704" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-22" value="Not Sufficient" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="inzhINmpMBKd6LQynHdK-21" vertex="1" connectable="0">
          <mxGeometry x="-0.1202" y="3" relative="1" as="geometry">
            <mxPoint x="-5" y="2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="swimlane2" value="DMS-HQ Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="380" y="-515" width="360" height="30" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="740" y="-515" width="340" height="1850" as="geometry" />
        </mxCell>
        <mxCell id="start1" value="A1. Create Parts Requisition" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="140" y="-454" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval1" value="A3a. HQ Approve Requisition" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="-244" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="generate_po" value="A4b. Generate Transfer Order" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="840" y="-25" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wait_goods" value="A5. Receive Goods" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="140" y="47" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="update_erp" value="A7. System Receive Data" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="840" y="155" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval2" value="B2. HQ Approve Damage Report" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="364" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision2" value="B3. Damage Approval Result?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="480" y="469" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="damage_to_erp" value="B4b. Receive Damage Info" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="840" y="585" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check_inventory" value="C3. Is Inventory Sufficient?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-297" y="625" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="picking" value="C4b. Execute Picking" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-284" y="777" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="modify_order" value="A3b. Modify/Redo Requisition" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="140" y="-135" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="modify_damage" value="B4a. Modify/Redo Damage Report" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="140" y="479" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="approval1" target="decision1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="Approved" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="decision1" target="generate_po" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" value="Rejected" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry x="-0.0068" relative="1" as="geometry">
            <mxPoint x="490.0000000000002" y="-104.99999999999977" as="sourcePoint" />
            <mxPoint x="280.0000000000002" y="-105" as="targetPoint" />
            <Array as="points">
              <mxPoint x="410" y="-105" />
              <mxPoint x="410" y="-105" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="generate_po" target="wait_goods" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="210" y="15" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-3" value="Ship" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge5" vertex="1" connectable="0">
          <mxGeometry x="-0.1284" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="wait_goods" target="inzhINmpMBKd6LQynHdK-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="approval2" target="decision2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge13" value="Approved" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="decision2" target="damage_to_erp" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge14" value="Rejected" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="decision2" target="modify_damage" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="picking" target="inzhINmpMBKd6LQynHdK-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-214" y="926" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="decision1" value="A3c. Approval Result?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="-145" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-4" value="A6. Parts Inventory Increase" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="141" y="155" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-4" target="update_erp" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="135" as="sourcePoint" />
            <mxPoint x="450" y="85" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-9" value="B1. Create Damage Report" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="144" y="266" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-10" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-9" target="approval2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="335" as="sourcePoint" />
            <mxPoint x="450" y="435" as="targetPoint" />
            <Array as="points">
              <mxPoint x="560" y="295" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-11" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="modify_damage" target="approval2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="475" as="sourcePoint" />
            <mxPoint x="450" y="425" as="targetPoint" />
            <Array as="points">
              <mxPoint x="210" y="395" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-12" value="C1. Create Work Order" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-286" y="385" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-13" value="C2. Generate Picking List" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-284" y="506" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-15" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="check_inventory" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-217" y="567" as="sourcePoint" />
            <mxPoint x="211" y="446" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.479;entryY=-0.011;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="check_inventory" target="picking" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-204" y="766" as="sourcePoint" />
            <mxPoint x="-217" y="803" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-17" value="Sufficient" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="inzhINmpMBKd6LQynHdK-16" vertex="1" connectable="0">
          <mxGeometry x="-0.0125" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-18" value="C5. Picking Complete" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-283" y="883" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="modify_order" target="approval1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="80" y="-105" as="sourcePoint" />
            <mxPoint x="80" y="-215" as="targetPoint" />
            <Array as="points">
              <mxPoint x="210" y="-214" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-19" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-10" y="687" as="sourcePoint" />
            <mxPoint x="-216" y="507" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-20" value="C4a. Picking List (Out of Stock)" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-38" y="636" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-20" target="start1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="50" y="615" as="sourcePoint" />
            <mxPoint x="60" y="-305" as="targetPoint" />
            <Array as="points">
              <mxPoint x="30" y="-295" />
              <mxPoint x="30" y="-424" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-24" value="C4a1. Lock Inventory" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-37" y="777" width="137" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-25" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="143" y="185" as="sourcePoint" />
            <mxPoint x="103.00000000000023" y="804.9999999999998" as="targetPoint" />
            <Array as="points">
              <mxPoint x="123" y="185" />
              <mxPoint x="123" y="805" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-26" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-24" target="picking" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="30" y="735" as="sourcePoint" />
            <mxPoint x="80" y="685" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-27" value="C6. Inventory Deduction" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-284" y="981" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-28" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-18" target="inzhINmpMBKd6LQynHdK-27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-70" y="1025" as="sourcePoint" />
            <mxPoint x="-20" y="975" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-30" value="D1. Return Excess Material" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-281" y="1109" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-31" value="D2. Inventory Increase" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-14" y="1109" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-32" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-30" target="inzhINmpMBKd6LQynHdK-31" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="1085" as="sourcePoint" />
            <mxPoint x="260" y="1035" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-35" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-50" y="980" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-36" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-27" target="inzhINmpMBKd6LQynHdK-35" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="955" as="sourcePoint" />
            <mxPoint x="260" y="905" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-37" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="853" y="715" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-38" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.48;entryY=-0.014;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="damage_to_erp" target="inzhINmpMBKd6LQynHdK-37" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="906" y="644.9999999999998" as="sourcePoint" />
            <mxPoint x="909" y="715" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-39" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="850" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-40" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="update_erp" target="inzhINmpMBKd6LQynHdK-39" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="475" as="sourcePoint" />
            <mxPoint x="400" y="425" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-45" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" parent="1" source="swimlane1" target="swimlane3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-50" y="705" as="sourcePoint" />
            <mxPoint y="655" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-1" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-8" y="1245" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="UP8bKdA2uHzj_HECdWaC-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="53" y="1169" as="sourcePoint" />
            <mxPoint x="42" y="1245" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-4" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-31" target="UP8bKdA2uHzj_HECdWaC-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="190" y="1175" as="sourcePoint" />
            <mxPoint x="240" y="1125" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-6" value="D3. Sync Inventory Changes" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="837" y="1108" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="vWPpz2ZaofT-5wo5VyPC-5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="start1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="-145" as="sourcePoint" />
            <mxPoint x="211" y="-335" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vWPpz2ZaofT-5wo5VyPC-6" value="A2. Production Part?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="141" y="-338" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="vWPpz2ZaofT-5wo5VyPC-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="vWPpz2ZaofT-5wo5VyPC-6" target="approval1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="-95" as="sourcePoint" />
            <mxPoint x="360" y="-145" as="targetPoint" />
            <Array as="points">
              <mxPoint x="560" y="-298" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="vWPpz2ZaofT-5wo5VyPC-9" value="yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="vWPpz2ZaofT-5wo5VyPC-7" vertex="1" connectable="0">
          <mxGeometry x="-0.7727" y="2" relative="1" as="geometry">
            <mxPoint x="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vWPpz2ZaofT-5wo5VyPC-10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=-0.024;entryY=0.355;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="vWPpz2ZaofT-5wo5VyPC-6" target="generate_po" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="-45" as="sourcePoint" />
            <mxPoint x="840" y="-10" as="targetPoint" />
            <Array as="points">
              <mxPoint x="100" y="-298" />
              <mxPoint x="100" y="-5" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="vWPpz2ZaofT-5wo5VyPC-11" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="vWPpz2ZaofT-5wo5VyPC-10" vertex="1" connectable="0">
          <mxGeometry x="-0.8131" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
