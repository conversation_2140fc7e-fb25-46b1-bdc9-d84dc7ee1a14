import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('./views/HomeView.vue')
  },
  {
    path: '/part-management',
    name: 'PartManagement',
    component: () => import('./views/PartManagementView.vue')
  },
  {
    path: '/parts-management-hq',
    name: 'PartsManagementHQ',
    component: () => import('./views/PartsManagementHQView.vue')
  },
  {
    path: '/parts-receipt',
    name: 'PartsReceipt',
    component: () => import('./views/PartsReceiptView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(router)
app.mount('#app')
