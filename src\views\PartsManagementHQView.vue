<template>
  <div class="parts-management-hq">
    <h1>零件管理 - 总部</h1>
    <p>总部零件管理和审批功能</p>
    
    <div class="hq-sections">
      <div class="section">
        <h3>叫料清单审批</h3>
        <p>审批门店提交的叫料清单</p>
        <div class="stats">
          <span class="stat">待审批: 5</span>
          <span class="stat">已审批: 23</span>
        </div>
      </div>
      
      <div class="section">
        <h3>库存管理</h3>
        <p>总部库存监控和管理</p>
        <div class="stats">
          <span class="stat">总库存: 1,234</span>
          <span class="stat">低库存: 12</span>
        </div>
      </div>
      
      <div class="section">
        <h3>供应商管理</h3>
        <p>供应商信息和采购管理</p>
        <div class="stats">
          <span class="stat">活跃供应商: 15</span>
          <span class="stat">待付款: 8</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PartsManagementHQView'
}
</script>

<style scoped>
.parts-management-hq {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 1rem;
}

.hq-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section h3 {
  color: #1890ff;
  margin-bottom: 1rem;
}

.stats {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
}

.stat {
  background: #f0f0f0;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
}
</style>
