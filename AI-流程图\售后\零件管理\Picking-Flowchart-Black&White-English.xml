<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="Picking Flowchart-Black&amp;White" id="picking-flow-bw-en">
    <mxGraphModel dx="901" dy="507" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="570" height="1360" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="DMS-HQ Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="610" y="40" width="280" height="1360" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="890" y="40" width="250" height="1360" as="geometry" />
        </mxCell>
        <mxCell id="start1" value="Generate Picking List" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="105" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check_inventory" value="1. System Check Outlet Parts Inventory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="213" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision1" value="2. Is Inventory Sufficient?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="160" y="321" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="picking" value="3a. Start Picking" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="724" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="complete_picking" value="4. Picking Complete" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="833" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="deduct_inventory" value="5. System Auto Deduct Outlet Inventory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="933" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision2" value="6. Need Return Material?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="180" y="1044" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="shortage_status" value="3b. Picking List Shows Out of Stock Status" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="420" y="331" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="create_request" value="4b. Create Parts Requisition" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="421" y="483" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval" value="5b. HQ Approve Requisition" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="671" y="484" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="generate_po" value="6b. Generate Transfer Order" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="941" y="608" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="start1" target="check_inventory" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="check_inventory" target="decision1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;fontColor=#000000;" parent="1" source="decision1" target="picking" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" value="No" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;fontColor=#000000;" parent="1" source="decision1" target="shortage_status" edge="1">
          <mxGeometry x="0.0118" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="picking" target="complete_picking" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="complete_picking" target="deduct_inventory" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="deduct_inventory" target="decision2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="shortage_status" target="create_request" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="create_request" target="approval" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge14" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="approval" target="generate_po" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-2" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="177" y="1279" width="118" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="decision2" target="wVClbFZ9tB9Vo7iyhRuW-20" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="1214" as="sourcePoint" />
            <mxPoint x="130" y="1200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="130" y="1084" />
              <mxPoint x="130" y="1196" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-21" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="wVClbFZ9tB9Vo7iyhRuW-3" vertex="1" connectable="0">
          <mxGeometry x="-0.7772" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-5" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="decision2" target="wVClbFZ9tB9Vo7iyhRuW-12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="900" as="sourcePoint" />
            <mxPoint x="430" y="1084" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-13" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="wVClbFZ9tB9Vo7iyhRuW-5" vertex="1" connectable="0">
          <mxGeometry x="-0.2272" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-6" value="7b. Ship" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="672" y="608" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="generate_po" target="wVClbFZ9tB9Vo7iyhRuW-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="710" as="sourcePoint" />
            <mxPoint x="580" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-8" value="8b. Receive parts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="421" y="608" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="wVClbFZ9tB9Vo7iyhRuW-6" target="wVClbFZ9tB9Vo7iyhRuW-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="640" as="sourcePoint" />
            <mxPoint x="570" y="590" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-10" value="9b. Lock Inventory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="421" y="725" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-11" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="wVClbFZ9tB9Vo7iyhRuW-8" target="wVClbFZ9tB9Vo7iyhRuW-10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="740" as="sourcePoint" />
            <mxPoint x="491" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-12" value="7a. Inventory Increase" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="387" y="1054" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="wVClbFZ9tB9Vo7iyhRuW-10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="751.0000000000002" as="sourcePoint" />
            <mxPoint x="310" y="755" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="wVClbFZ9tB9Vo7iyhRuW-12" target="wVClbFZ9tB9Vo7iyhRuW-20" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="482" y="1114" as="sourcePoint" />
            <mxPoint x="309" y="1195.9999999999998" as="targetPoint" />
            <Array as="points">
              <mxPoint x="457" y="1181" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-20" value="8. Technician Complete Work" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="167" y="1166" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="wVClbFZ9tB9Vo7iyhRuW-20" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="1170" as="sourcePoint" />
            <mxPoint x="950" y="1196" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-23" value="9. Sync Inventory Changes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="951" y="1167" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wVClbFZ9tB9Vo7iyhRuW-24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="wVClbFZ9tB9Vo7iyhRuW-20" target="wVClbFZ9tB9Vo7iyhRuW-2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="1226" as="sourcePoint" />
            <mxPoint x="236.00000000000023" y="1279" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
