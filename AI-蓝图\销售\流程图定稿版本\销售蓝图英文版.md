### **4.1.1 Prospect Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ------------------------ | ------------- | ---------------------------------------------------- |
| 1 | Registers/Uses Super APP |  | The customer completes registration and performs actions on the Super APP. |
| 1a | Book the vehicle | Sales Order | The customer book a vehicle through the Super APP. |
| 1b | Update prospect info | Prospect file | Super APP syncs prospect information to the DMS HQ. |
| 2 | Has registered the Super APP? | - | The SA determines if the prospect has registered for the Super APP. |
| 2a | Manually Creates Prospect File | Prospect file | The SA manually creates a prospect file in DMS for customers who have not registered for the APP. |
| 2b | Already associated by an SA from the same outlet? | - | The system determines if the prospect has already been followed up by another SA from the same outlet. |
| 3 | Follow up& Update Interest level | Prospect file | The SA follows up the prospect and updates their intention level based on the communication. |
| 4 | Test Drive(if needed) | Test Drive Order | The SA arranges and records the customer's test drive activity. |
| 5 | Does the prospect want to purchase? | - | After following up, the SA determines if the customer has an intention to purchase. |
| 6 | Create sales order | Sales Order | The SA creates a sales order for a customer with a clear intention to purchase. |

### **4.1.2 Test Drive Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ---------------- | ------------- | ----------------------------------------- |
| 1 | Has the prospect registered the SUPER APP? | - | The SA confirms if the customer has registered for the Super APP. |
| 1a | Registers SUPER APP | - | The customer completes registration on the Super APP. |
| 2 | Update prospect info | Prospect file | The DMS HQ system updates the prospect file based on the APP registration information. |
| 3 | Create Test Drive Order | Test Drive Order | The SA creates a test drive order for the customer. |
| 4 | Fill in Test Drive details | Test Drive Order | The SA records detailed test drive information on the test drive order. |
| 5 | Query and analyze data | - | The DMS HQ system provides functions for querying and analyzing test drive data. |

### **4.1.3 Order Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ----------------------------------- | ------------- | ---------------------------------------------------- |
| 1a | Create sales order | Sales Order | The customer creates an order through the Super APP. |
| 1b | SA creates order | Sales Order | The SA creates an order for the customer in the DMS system. |
| 2 | Pay deposit within 72h? | - | The system checks if the customer has paid the booking fee within the specified time. |
| 2a | Order cancelled | Sales Order | The system or SA cancels the order if the booking fee is not paid within the specified time. |
| 3 | Deposit paid | - | The customer pays the order booking fee. |
| 4 | Need to modify personal info? | - | The SA confirms if the customer needs to modify personal information in the order. |
| 4a | Submit modification request | - | The customer submits a request to modify order information through the Super APP. |
| 5 | Need to change vehicle color? | - | The SA confirms if the customer needs to modify the vehicle color. |
| 5a1 | Sales order approval-Outlet | Sales Order | The outlet sales manager approves the request to modify vehicle color information. |
| 6 | Need to cancel order? | - | The customer or SA initiates the order cancellation process. |
| 6a1 | Sales order approval | Sales Order | The outlet sales manager approves the order cancellation request. |
| 6a3 | Execute refund | - | The Finance Staff processes the booking fee refund. |
| 7 | Select payment method | - | The customer chooses between full payment or a loan as the payment method. |
| 8 | SA helps customer apply for bank loan (offline) | - | The SA assists the customer in preparing and submitting an offline loan application. |
| 9 | Loan approval? | - | The bank approves the customer's loan application. |
| 10 | Apply for vehicle allocation | - | The SA submits a vehicle Allocation Request for the order. |
| 11 | Execute vehicle allocation | - | The DMS HQ allocation staff executes the allocation based on the order and inventory. |
| 12 | Update VIN | Sales Order | After successful allocation, the vehicle VIN is updated in the corresponding order. |
| 13 | Execute insurance purchase | Insurance Policy | The insurance staff processes the vehicle insurance application through the VIPRO system. |
| 14 | Pay customs duties | - | The customer pays the required customs duty for the vehicle. |
| 15 | Execute JPJ registration | - | The registration staff completes the vehicle registration through the JPJ system. |
| 16 | Update registration info | Sales Order | After registration is complete, the related information is updated in the order. |
| 17 | Confirm payment settled | - | The Finance Staff confirms that the customer has cleared the down payment or full payment. |
| 18 | Customer confirms delivery | Delivery Order | The customer confirms the vehicle delivery offline or through the APP. |
| 19 | Receive sales order info | Sales Order | The ERP system receives the created sales order information from DMS. |
| 20 | Receive financial info | - | The ERP system receives financial data such as booking fees, refunds, customs duties, and down payments or full payments from DMS. |
| 21 | Receive delivery info | Delivery Order | The ERP system receives data on the vehicle delivery from DMS. |

### **4.1.4 Payment and Refund Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ---------------------- | ------------- | ------------------------------------------------------------ |
| 1 | Complete payment/refund operation | - | The customer completes the payment or refund operation on the Super APP. |
| 2 | Create payment/refund information | - | The DMS Outlet automatically/manually creates payment/refund information. |
| 3 | Business Type? | - | The system determines if the current operation is a payment or a refund. |
| 3a | Check order details & approval status | - | Before processing a refund, the Finance Staff needs to confirm the details and approval status of the associated order. |
| 4 | Confirm paymen/refund amount and method | - | The Finance Staff or SA confirms the amount and payment method of the form. |
| 5 | Is the financial info correct? | - | The Finance Staff verifies the accuracy of the financial information. |
| 6 | Save information | - | The system saves the final confirmed payment or refund information. |
| 7 | Receive financial data | - | The ERP system receives the payment or refund financial data from DMS. |

### **4.1.5 Allocation Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ---------------- | ------------- | ----------------------------------------------- |
| 1 | Vehicle Data Generation | - | The ERP system generates detailed vehicle data, including the VIN. |
| 2 | View Vehicle Resources of All Outlets | - | The DMS HQ system displays all available vehicle resources. |
| 3 | View Vehicle Resources of Outlet | - | The DMS Outlet system displays the vehicle resources allocated to the outlet. |
| 4 | Confirm Vehicle Inbound | - | The outlet confirms that the vehicle has physically arrived and processes its inbound. |
| 4a | Receive Outlet Inbound Info | - | The ERP system receives the vehicle inbound information synced from DMS. |
| 5 | Submit Allocation | - | The SA submits a vehicle allocation request for a sales order. |
| 6 | Approval? | - | The DMS HQ allocation staff approves the allocation request submitted by the outlet. |
| 7 | Confirms Allocation | Sales Order | The DMS HQ allocation staff confirms the allocation, assigning the vehicle to the specified order. |
| 8 | View Allocated info | Sales Order | The outlet SA views the allocation result for the order. |

### **4.1.6 Insurance Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | -------------------- | ------------- | ------------------------------------------------ |
| 1 | Confirm insurance plan with customer offline | - | The SA communicates with the customer offline to confirm the final insurance plan. |
| 2 | Push order info | - | The DMS Outlet pushes the order information that requires insurance to the VIPRO insurance system. |
| 3 | Insurance application successful? | - | The VIPRO insurance system processes the application and returns the result. |
| 3a | Generates policy info | Insurance Policy | Upon successful application, the VIPRO system generates a formal electronic insurance policy. |
| 3b | Insurance application again? | - | If the application fails, the SA confirms whether to re-apply. |
| 4 | Update insurance info | Sales Order | The DMS Outlet receives and updates the insurance policy information associated with the order. |

### **4.1.7 Vehicle Registration Management (JPJ)**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ------------------------- | ------------- | ---------------------------------------------------- |
| 1 | Pay customs duties | - | The customer pays the required customs duty for the vehicle. |
| 1a | Receive financial info | - | The ERP system receives the financial data for the paid customs duty from DMS. |
| 2 | Verifie the vehicle registration info | - | The SA prepares and confirms the vehicle registration information to be submitted in the DMS system. |
| 3 | Push vehicle info to JPJ System | - | The registration staff pushes the vehicle registration information to the JPJ vehicle registration system. |
| 4 | JPJ registration result? | - | The JPJ system returns the result of the vehicle registration (success or failure). |
| 4a | Resubmit? | - | If registration fails, the SA decides whether to modify the information and resubmit. |
| 5 | Update Registration info | Sales Order | Upon successful registration, the DMS system updates the vehicle registration status and information in the order. |

### **4.1.8 Invoice Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ------------ | ------------- | ------------------------------------------------------------ |
| 1 | Submit delivery | Delivery Order | After the vehicle delivery process is completed, the system triggers the invoice issuance process. |
| 2 | Invoice issuance completed | Invoice | The system or Finance Staff completes the issuance of the invoice. |
| 2a | Receive invoice information | Invoice | The ERP system receives the issued invoice information from DMS. |
| 3 | Operation Type? | - | The user selects the desired operation to be performed on the invoice. |
| 3a | View invoice details | Invoice | The user views the detailed information of a specific invoice in the DMS system. |
| 3b | Print invoice | Physical Invoice | The user prints a physical invoice through the DMS system. |
| 3c | Send E-invoice | Electronic Invoice | DMS is integrated with ERP; the user sends the e-invoice to ERP, which then sends it to the customer. |

### **4.1.9 Delivery Management**

| **No.** | **Activity** | **Documents** | **Description** |
| ------- | ------------------ | ------------- | ------------------------------------------------------------ |
| 1 | Submit for delivery | - | The SA submits a vehicle delivery request in DMS. |
| 2 | Ready for delivery? | - | The system determines if the delivery conditions are met based on order status, vehicle status, and payment status. |
| 3 | Generate delivery order | Delivery Order | Once the conditions are met, the system automatically generates a delivery order. |
| 4 | Order Source? | - | The system determines if the order was created via the Super APP or offline. |
| 4a | Send delivery order info to APP | Delivery Order | For online orders, the SA pushes the delivery order information to the customer's Super APP. |
| 4a1 | Customer confirms delivery | Delivery Order | The customer confirms the delivery information on the Super APP. |
| 4b | Fill in delivery confirmation info | Delivery Order | For offline orders, the SA manually enters the delivery information into the system. |
| 5 | Update delivery status | Sales Order | After the customer confirms the delivery, the system updates the final status of the order and vehicle to "Delivered". |
| 6 | Payment method? | - | The system determines if the payment is full or via a loan based on the order information. |
| 7 | Submit delivery document to the bank | - | For loan purchases, the completed delivery documents must be submitted to the bank for loan disbursement. |
| 8 | Receive delivery&finance data | - | The ERP system receives the delivery confirmation and related financial settlement data from DMS. | 