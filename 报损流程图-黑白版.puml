@startuml 报损流程图
!theme plain
skinparam title {
    RoundCorner 15
    BorderThickness 2
    BorderColor #000000
    BackgroundColor #FFFFFF
    FontColor #000000
    FontSize 24
}
skinparam swimlane {
    BorderColor #000000
    BorderThickness 2
    TitleFontColor #000000
    TitleBackgroundColor #FFFFFF
    TitleFontSize 16
}
skinparam activity {
    BorderColor #000000
    BackgroundColor #FFFFFF
    FontColor #000000
    Shadowing false
}
skinparam note {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
}
skinparam arrow {
    Color #000000
    Thickness 2
}

title 报损流程图

|DMS-店端|
start
:门店操作员收货时发现零件破损;
:门店操作员新建报损单;
:门店操作员提交报损单;

|DMS-厂端|
label 审批流程
:HQ审批员审批报损单;

if (审批结果?) then (通过)
  |DMS-店端|
  :系统自动标记零件为破损件;
  note right: 在库存报表中标记
  :系统从可用库存中扣除;
  stop
else (未通过)
  |DMS-店端|
  :门店操作员修改报损单;
  :系统重新提交审批;
  goto 审批流程
endif

@enduml
