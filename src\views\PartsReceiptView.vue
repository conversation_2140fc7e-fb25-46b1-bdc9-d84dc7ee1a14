<template>
  <div class="parts-receipt">
    <h1>零件收货</h1>
    <p>零件收货确认和处理</p>
    
    <div class="receipt-sections">
      <div class="section">
        <h3>待收货清单</h3>
        <p>等待收货确认的零件</p>
        <div class="receipt-list">
          <div class="receipt-item" v-for="item in pendingReceipts" :key="item.id">
            <div class="item-info">
              <strong>{{ item.partName }}</strong>
              <span>数量: {{ item.quantity }}</span>
              <span>供应商: {{ item.supplier }}</span>
            </div>
            <button class="btn btn-confirm" @click="confirmReceipt(item.id)">
              确认收货
            </button>
          </div>
        </div>
      </div>
      
      <div class="section">
        <h3>收货历史</h3>
        <p>已完成的收货记录</p>
        <div class="history-stats">
          <span class="stat">今日收货: 12</span>
          <span class="stat">本月收货: 156</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PartsReceiptView',
  data() {
    return {
      pendingReceipts: [
        {
          id: 1,
          partName: '刹车片',
          quantity: 10,
          supplier: '博世汽车'
        },
        {
          id: 2,
          partName: '机油滤清器',
          quantity: 20,
          supplier: '曼牌滤清器'
        },
        {
          id: 3,
          partName: '火花塞',
          quantity: 8,
          supplier: 'NGK'
        }
      ]
    }
  },
  methods: {
    confirmReceipt(id) {
      this.pendingReceipts = this.pendingReceipts.filter(item => item.id !== id)
      alert('收货确认成功！')
    }
  }
}
</script>

<style scoped>
.parts-receipt {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 1rem;
}

.receipt-sections {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section h3 {
  color: #1890ff;
  margin-bottom: 1rem;
}

.receipt-list {
  margin-top: 1rem;
}

.receipt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-info strong {
  color: #333;
}

.item-info span {
  color: #666;
  font-size: 0.9rem;
}

.btn {
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-confirm {
  background: #52c41a;
  color: white;
}

.btn-confirm:hover {
  background: #73d13d;
}

.history-stats {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat {
  background: #f0f0f0;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
}
</style>
