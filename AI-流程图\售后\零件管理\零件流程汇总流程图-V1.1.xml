<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="零件流程汇总流程图-V1.1优化版" id="parts-process-flow-v11">
    <mxGraphModel dx="2777" dy="877" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="2500" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="inzhINmpMBKd6LQynHdK-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;strokeColor=none;" parent="1" source="swimlane1" target="swimlane3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="swimlane1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-360" y="40" width="750" height="1720" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.471;entryY=0.011;entryDx=0;entryDy=0;entryPerimeter=0;" parent="swimlane1" target="inzhINmpMBKd6LQynHdK-13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="177" y="921.9999999999998" as="sourcePoint" />
            <mxPoint x="179" y="992" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-21" value="" style="endArrow=classic;html=1;rounded=0;" parent="swimlane1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="233" y="1050" as="sourcePoint" />
            <mxPoint x="352" y="1049.3703703703704" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-22" value="不满足" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="inzhINmpMBKd6LQynHdK-21" vertex="1" connectable="0">
          <mxGeometry x="-0.1202" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="swimlane2" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="390" y="40" width="350" height="30" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="ERP系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="750" y="40" width="340" height="1720" as="geometry" />
        </mxCell>
        <mxCell id="start1" value="A1. 新建叫料清单" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="150" y="100" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval1" value="A2. HQ审批叫料单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="181" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="generate_po" value="A4b.&amp;nbsp; 生成转库单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="850" y="400" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="wait_goods" value="A5. 收货" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="150" y="472" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="update_erp" value="A7. 系统接收收货数据" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="850" y="580" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval2" value="B2. HQ审批报损单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="789" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision2" value="B3. 报损审批结果?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="894" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="damage_to_erp" value="B4b.接收报损信息" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="850" y="1010" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check_inventory" value="C3. 库存是否满足?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-287" y="1050" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="picking" value="C4b. 执行拣货" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-274" y="1202" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="modify_order" value="A4a. 修改或重做叫料单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="150" y="290" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="modify_damage" value="B4a.&amp;nbsp; 修改或重做报损单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="150" y="904" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="start1" target="approval1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="approval1" target="decision1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="decision1" target="generate_po" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" value="未通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry x="-0.0068" relative="1" as="geometry">
            <mxPoint x="500.0000000000002" y="320.0000000000002" as="sourcePoint" />
            <mxPoint x="290.0000000000002" y="320" as="targetPoint" />
            <Array as="points">
              <mxPoint x="420" y="320" />
              <mxPoint x="420" y="320" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="generate_po" target="wait_goods" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-3" value="发货" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge5" vertex="1" connectable="0">
          <mxGeometry x="-0.1284" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="wait_goods" target="inzhINmpMBKd6LQynHdK-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="220" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="approval2" target="decision2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge13" value="通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="decision2" target="damage_to_erp" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge14" value="未通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="decision2" target="modify_damage" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="picking" target="inzhINmpMBKd6LQynHdK-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-204" y="1351" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="decision1" value="A3. 审批结果?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="280" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-4" value="A6. 零件库存增加" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="151" y="580" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-4" target="update_erp" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="560" as="sourcePoint" />
            <mxPoint x="460" y="510" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-9" value="B1. 新建报损单" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="154" y="691" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-10" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-9" target="approval2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="760" as="sourcePoint" />
            <mxPoint x="460" y="860" as="targetPoint" />
            <Array as="points">
              <mxPoint x="570" y="720" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-11" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="modify_damage" target="approval2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="900" as="sourcePoint" />
            <mxPoint x="460" y="850" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="820" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-12" value="C1. 创建工单" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-276" y="810" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-13" value="C2. 生成拣货单" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-274" y="931" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-15" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="check_inventory" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-207" y="992" as="sourcePoint" />
            <mxPoint x="221" y="871" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.479;entryY=-0.011;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="check_inventory" target="picking" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-194" y="1191" as="sourcePoint" />
            <mxPoint x="-207" y="1228" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-17" value="满足" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="inzhINmpMBKd6LQynHdK-16" vertex="1" connectable="0">
          <mxGeometry x="-0.0125" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-18" value="C5. 拣货完毕" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-273" y="1308" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="modify_order" target="approval1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="320" as="sourcePoint" />
            <mxPoint x="90" y="210" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="211" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-19" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint y="1112" as="sourcePoint" />
            <mxPoint x="-206" y="932" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-20" value="C4a. 拣货单（缺货状态）" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-28" y="1061" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-20" target="start1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="1040" as="sourcePoint" />
            <mxPoint x="70" y="120" as="targetPoint" />
            <Array as="points">
              <mxPoint x="40" y="130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-24" value="&amp;nbsp;C4a1. 锁定库存" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-27" y="1202" width="137" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-25" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="153" y="610" as="sourcePoint" />
            <mxPoint x="113.00000000000023" y="1229.9999999999998" as="targetPoint" />
            <Array as="points">
              <mxPoint x="133" y="610" />
              <mxPoint x="133" y="1230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-26" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-24" target="picking" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="1160" as="sourcePoint" />
            <mxPoint x="90" y="1110" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-27" value="C6. 库存扣减" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-274" y="1406" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-28" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-18" target="inzhINmpMBKd6LQynHdK-27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-60" y="1450" as="sourcePoint" />
            <mxPoint x="-10" y="1400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-30" value="D1. 余料退拣" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-271" y="1534" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-31" value="D2. 库存增加" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-4" y="1534" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-32" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-30" target="inzhINmpMBKd6LQynHdK-31" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="1510" as="sourcePoint" />
            <mxPoint x="270" y="1460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-35" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-40" y="1405" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-36" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="inzhINmpMBKd6LQynHdK-27" target="inzhINmpMBKd6LQynHdK-35" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="1380" as="sourcePoint" />
            <mxPoint x="270" y="1330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-37" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="863" y="1140" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-38" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.48;entryY=-0.014;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="damage_to_erp" target="inzhINmpMBKd6LQynHdK-37" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="916" y="1069.9999999999998" as="sourcePoint" />
            <mxPoint x="919" y="1140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-39" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="860" y="695" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-40" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="update_erp" target="inzhINmpMBKd6LQynHdK-39" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="900" as="sourcePoint" />
            <mxPoint x="410" y="850" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="inzhINmpMBKd6LQynHdK-45" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" parent="1" source="swimlane1" target="swimlane3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-40" y="1130" as="sourcePoint" />
            <mxPoint x="10" y="1080" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-1" value="结束" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="2" y="1670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="UP8bKdA2uHzj_HECdWaC-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="63" y="1594" as="sourcePoint" />
            <mxPoint x="52" y="1670" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-4" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="inzhINmpMBKd6LQynHdK-31" target="UP8bKdA2uHzj_HECdWaC-6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="1600" as="sourcePoint" />
            <mxPoint x="250" y="1550" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="UP8bKdA2uHzj_HECdWaC-6" value="D3. 同步库存变动信息" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="847" y="1533" width="140" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
