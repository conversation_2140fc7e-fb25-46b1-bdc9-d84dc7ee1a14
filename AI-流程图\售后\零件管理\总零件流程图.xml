<mxfile host="app.diagrams.net" modified="2024-12-26T10:05:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" etag="abc123def456" version="22.1.11" type="device">
  <diagram name="门店叫料清单审批流程" id="flow1">
    <mxGraphModel dx="1422" dy="750" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 泳道定义 -->
        <mxCell id="lane1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;fillColor=#ECF5FF;strokeColor=#DCDFE6;fontColor=#303133;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="280" height="800" as="geometry" />
        </mxCell>

        <mxCell id="lane2" value="DMS-厂端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;fillColor=#ECF5FF;strokeColor=#DCDFE6;fontColor=#303133;" vertex="1" parent="1">
          <mxGeometry x="320" y="40" width="280" height="800" as="geometry" />
        </mxCell>

        <mxCell id="lane3" value="ERP系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;fillColor=#ECF5FF;strokeColor=#DCDFE6;fontColor=#303133;" vertex="1" parent="1">
          <mxGeometry x="600" y="40" width="280" height="800" as="geometry" />
        </mxCell>

        <mxCell id="lane4" value="库存管理系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;startSize=30;fillColor=#ECF5FF;strokeColor=#DCDFE6;fontColor=#303133;" vertex="1" parent="1">
          <mxGeometry x="880" y="40" width="280" height="800" as="geometry" />
        </mxCell>

        <!-- 流程节点 -->
        <mxCell id="node1" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="120" y="90" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node2" value="门店操作员新建叫料清单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node3" value="门店操作员提交审批" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="100" y="270" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node4" value="HQ审批员审核叫料清单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="380" y="270" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node5" value="审批是否通过?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="400" y="370" width="120" height="80" as="geometry" />
        </mxCell>

        <mxCell id="node6" value="系统自动处理将数据传输给ERP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="380" y="490" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node7" value="系统自动处理生成采购订单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="660" y="490" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node8" value="门店操作员等待收货" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="100" y="580" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node9" value="门店操作员完成收货确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="100" y="670" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node10" value="系统推送收货数据给ERP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="100" y="760" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node11" value="系统自动处理更新库存报表" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="940" y="670" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node12" value="零件数增加" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="940" y="760" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node13" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="960" y="850" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node14" value="门店选择?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="120" y="370" width="120" height="80" as="geometry" />
        </mxCell>

        <mxCell id="node15" value="门店操作员修改叫料清单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="40" y="490" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node16" value="系统自动生成新叫料单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="40" y="580" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="node17" value="门店操作员新建叫料清单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F7FA;strokeColor=#409EFF;fontColor=#606266;" vertex="1" parent="1">
          <mxGeometry x="200" y="490" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 连接线 -->
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node1" target="node2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node2" target="node3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node3" target="node4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node4" target="node5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge5" value="通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node5" target="node6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node6" target="node7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node7" target="node8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="740" y="610" />
              <mxPoint x="180" y="610" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node8" target="node9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node9" target="node10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node10" target="node11">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="850" />
              <mxPoint x="1020" y="850" />
              <mxPoint x="1020" y="700" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node11" target="node12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node12" target="node13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge13" value="不通过" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node5" target="node14">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="410" />
              <mxPoint x="180" y="410" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge14" value="修改原单" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node14" target="node15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node15" target="node16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge16" value="新建叫料单" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node14" target="node17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge17" value="重新提交" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node16" target="node3">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="100" y="650" />
              <mxPoint x="20" y="650" />
              <mxPoint x="20" y="300" />
              <mxPoint x="100" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge18" value="重新提交" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#409EFF;strokeWidth=2;" edge="1" parent="1" source="node17" target="node3">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="260" y="560" />
              <mxPoint x="340" y="560" />
              <mxPoint x="340" y="300" />
              <mxPoint x="260" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
