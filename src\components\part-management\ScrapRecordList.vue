<template>
  <div class="scrap-record-list">
    <h3>报废记录</h3>
    <div class="record-list">
      <div v-for="record in records" :key="record.id" class="record-item">
        <div class="record-info">
          <strong>{{ record.partName }}</strong>
          <span>编号: {{ record.partNumber }}</span>
          <span>数量: {{ record.quantity }}</span>
          <span>原因: {{ getReasonText(record.reason) }}</span>
          <span>日期: {{ record.date }}</span>
        </div>
        <div class="record-status" :class="record.status">
          {{ getStatusText(record.status) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScrapRecordList',
  data() {
    return {
      records: [
        {
          id: 1,
          partName: '刹车片',
          partNumber: 'BP001',
          quantity: 2,
          reason: 'damaged',
          date: '2024-12-25',
          status: 'approved'
        },
        {
          id: 2,
          partName: '机油滤清器',
          partNumber: 'OF002',
          quantity: 1,
          reason: 'expired',
          date: '2024-12-24',
          status: 'pending'
        },
        {
          id: 3,
          partName: '火花塞',
          partNumber: 'SP003',
          quantity: 4,
          reason: 'defective',
          date: '2024-12-23',
          status: 'rejected'
        }
      ]
    }
  },
  methods: {
    getReasonText(reason) {
      const reasons = {
        damaged: '损坏',
        expired: '过期',
        defective: '质量缺陷',
        obsolete: '淘汰',
        other: '其他'
      }
      return reasons[reason] || reason
    },
    getStatusText(status) {
      const statuses = {
        pending: '待审批',
        approved: '已批准',
        rejected: '已拒绝'
      }
      return statuses[status] || status
    }
  }
}
</script>

<style scoped>
.scrap-record-list {
  margin-top: 2rem;
}

h3 {
  color: #333;
  margin-bottom: 1rem;
}

.record-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.record-info strong {
  color: #333;
  font-size: 1.1rem;
}

.record-info span {
  color: #666;
  font-size: 0.9rem;
}

.record-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.record-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.record-status.approved {
  background: #f6ffed;
  color: #52c41a;
}

.record-status.rejected {
  background: #fff2f0;
  color: #ff4d4f;
}
</style>
