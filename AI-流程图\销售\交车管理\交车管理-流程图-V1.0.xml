<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0" pages="3">
  <diagram name="交车管理流程图" id="交车管理流程图">
    <mxGraphModel dx="1332" dy="605" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="2500" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="DMS-店端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="160" y="40" width="560" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="SUPER APP端（客户）" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="720" y="40" width="380" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3" value="ERP系统" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="1100" y="40" width="380" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="start" target="_ZMWxJk2WRy10vGp5s_v-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="condition_check" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;2.系统验证是否达到交车标准&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="300" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="create_delivery" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;2a.系统生成交车单&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="420" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="view_delivery" target="_ZMWxJk2WRy10vGp5s_v-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="view_delivery" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;3.销售顾问查看交车单&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="530" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="update_pending" target="a04MswXCqQy-bJsFU_pF-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="update_pending" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4a1.操作记录日志&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="760" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="customer_app_confirm" target="a04MswXCqQy-bJsFU_pF-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="910" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-13" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a04MswXCqQy-bJsFU_pF-12" vertex="1" connectable="0">
          <mxGeometry x="-0.8066" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="customer_app_confirm" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4a3.客户APP是否确认？&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="840" y="880" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="app_confirm_action" target="app_confirm_process" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="app_confirm_action" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4a3a.客户在APP端确认交车&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="830" y="1010" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="app_confirm_process" target="uJskJp1dAvQ9XYYpbKpo-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="app_confirm_process" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;5.更新交车状态&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="540" y="1010" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="erp_sync" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;7.交车完成信息&lt;/font&gt;&lt;/font&gt;&lt;br&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;同步交车数据到ERP系统&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1220" y="1130" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="condition_check" target="create_delivery" edge="1">
          <mxGeometry x="0.0051" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="create_delivery" target="view_delivery" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge16" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="customer_app_confirm" target="app_confirm_action" edge="1">
          <mxGeometry x="-0.0037" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="_ZMWxJk2WRy10vGp5s_v-3" target="condition_check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-3" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;1.订单管理&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;销售顾问在订单编辑页面“提交交车”&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
          <mxGeometry x="280" y="200" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="condition_check" target="_ZMWxJk2WRy10vGp5s_v-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="510" y="335" />
              <mxPoint x="510" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-7" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="_ZMWxJk2WRy10vGp5s_v-6" vertex="1" connectable="0">
          <mxGeometry x="-0.8203" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="_ZMWxJk2WRy10vGp5s_v-9" target="a04MswXCqQy-bJsFU_pF-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-9" value="APP创建" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a04MswXCqQy-bJsFU_pF-3" vertex="1" connectable="0">
          <mxGeometry x="-0.4842" y="1" relative="1" as="geometry">
            <mxPoint x="12" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-9" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4.订单来源&lt;/font&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="630" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="a04MswXCqQy-bJsFU_pF-1" target="update_pending" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-1" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4a.销售顾问主动交车单信息至APP端&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="635" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="a04MswXCqQy-bJsFU_pF-4" target="customer_app_confirm" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-4" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4a2.客户在AP​​P端收到确认交车通知&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="830" y="760" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="a04MswXCqQy-bJsFU_pF-8" target="app_confirm_process">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="360" y="1040" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-8" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;4b.销售顾问&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;填写客户交车确认信息&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="280" y="880" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.532;entryY=-0.035;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="_ZMWxJk2WRy10vGp5s_v-9" target="a04MswXCqQy-bJsFU_pF-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-11" value="线下创建" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a04MswXCqQy-bJsFU_pF-10" vertex="1" connectable="0">
          <mxGeometry x="-0.6407" y="-1" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-23" value="结束" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="555" y="1250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="uJskJp1dAvQ9XYYpbKpo-2" target="erp_sync">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="uJskJp1dAvQ9XYYpbKpo-2" target="a04MswXCqQy-bJsFU_pF-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-2" value="&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;6.订单管理&lt;/font&gt;&lt;/font&gt;&lt;br&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;&lt;font style=&quot;vertical-align: inherit;&quot;&gt;更新订单状态&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
          <mxGeometry x="555" y="1130" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="交车管理流程图_en" id="交车管理流程图_en">
    <mxGraphModel dx="1665" dy="756" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="2500" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1_en" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="160" y="40" width="560" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2_en" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="720" y="40" width="380" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="swimlane3_en" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="1100" y="40" width="380" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-4_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="start_en" target="_ZMWxJk2WRy10vGp5s_v-3_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="start_en" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="condition_check_en" value="2.&amp;nbsp;Ready for delivery?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="300" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="create_delivery_en" value="2a.System generates delivery order" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="420" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-11_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="view_delivery_en" target="_ZMWxJk2WRy10vGp5s_v-9_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="view_delivery_en" value="3.SA views delivery order" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="530" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-5_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="update_pending_en" target="a04MswXCqQy-bJsFU_pF-4_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="update_pending_en" value="4a1.Record operation log" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="760" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-12_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="customer_app_confirm_en" target="a04MswXCqQy-bJsFU_pF-8_en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="910" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-13_en" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a04MswXCqQy-bJsFU_pF-12_en" vertex="1" connectable="0">
          <mxGeometry x="-0.8066" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="customer_app_confirm_en" value="4a3.Customer APP confirmation?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="840" y="880" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-7_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="app_confirm_action_en" target="app_confirm_process_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="app_confirm_action_en" value="4a3a.Customer confirms delivery on APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="830" y="1010" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-3_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="app_confirm_process_en" target="uJskJp1dAvQ9XYYpbKpo-2_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="app_confirm_process_en" value="5.Update delivery status" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="540" y="1010" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="erp_sync_en" value="7.Delivery completion information&lt;br&gt;Sync delivery data to ERP System" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1220" y="1130" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge3_en" value="Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="condition_check_en" target="create_delivery_en" edge="1">
          <mxGeometry x="0.0051" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge6_en" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="create_delivery_en" target="view_delivery_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge16_en" value="Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="customer_app_confirm_en" target="app_confirm_action_en" edge="1">
          <mxGeometry x="-0.0037" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-5_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="_ZMWxJk2WRy10vGp5s_v-3_en" target="condition_check_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-3_en" value="1.Order Management&lt;br&gt;SA submits for delivery" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
          <mxGeometry x="280" y="200" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-6_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="condition_check_en" target="_ZMWxJk2WRy10vGp5s_v-3_en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="510" y="335" />
              <mxPoint x="510" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-7_en" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="_ZMWxJk2WRy10vGp5s_v-6_en" vertex="1" connectable="0">
          <mxGeometry x="-0.8203" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-3_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="_ZMWxJk2WRy10vGp5s_v-9_en" target="a04MswXCqQy-bJsFU_pF-1_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-9_en" value="Super APP" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a04MswXCqQy-bJsFU_pF-3_en" vertex="1" connectable="0">
          <mxGeometry x="-0.4842" y="1" relative="1" as="geometry">
            <mxPoint x="12" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="_ZMWxJk2WRy10vGp5s_v-9_en" value="4.Order Source" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="630" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-2_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="a04MswXCqQy-bJsFU_pF-1_en" target="update_pending_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-1_en" value="4a.SA proactively sends delivery order information to APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="635" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-6_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="a04MswXCqQy-bJsFU_pF-4_en" target="customer_app_confirm_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-4_en" value="4a2.Customer receives delivery confirmation notification on APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="830" y="760" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-7_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="a04MswXCqQy-bJsFU_pF-8_en" target="app_confirm_process_en" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="360" y="1040" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-8_en" value="4b.SA fills in customer delivery confirmation information" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="280" y="880" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-10_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.532;entryY=-0.035;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="_ZMWxJk2WRy10vGp5s_v-9_en" target="a04MswXCqQy-bJsFU_pF-8_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-11_en" value="Offline" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a04MswXCqQy-bJsFU_pF-10_en" vertex="1" connectable="0">
          <mxGeometry x="-0.6407" y="-1" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a04MswXCqQy-bJsFU_pF-23_en" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="555" y="1250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-4_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="uJskJp1dAvQ9XYYpbKpo-2_en" target="erp_sync_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-6_en" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="uJskJp1dAvQ9XYYpbKpo-2_en" target="a04MswXCqQy-bJsFU_pF-23_en" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uJskJp1dAvQ9XYYpbKpo-2_en" value="6.Order Management&lt;br&gt;Update order status" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
          <mxGeometry x="555" y="1130" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="1ghNWQda-CRGZb7LHtoA" name="第 3 页">
    <mxGraphModel dx="996" dy="450" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="690" pageHeight="980" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="w0igsXaTGg2iQcfhWhBG-1" value="DMS-Outlet" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="68" y="22" width="536" height="956" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-2" value="SUPER APP (Customer)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="604" y="22" width="380" height="956" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-3" value="ERP System" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="984" y="22" width="380" height="956" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-5" target="w0igsXaTGg2iQcfhWhBG-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="244" y="182" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-5" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="184" y="82" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-6" target="w0igsXaTGg2iQcfhWhBG-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="363" y="317" />
              <mxPoint x="363" y="214" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-45" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="w0igsXaTGg2iQcfhWhBG-44" vertex="1" connectable="0">
          <mxGeometry x="-0.8493" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-6" value="2.&amp;nbsp;Ready for delivery?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="154" y="282" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-7" target="w0igsXaTGg2iQcfhWhBG-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-7" value="3.Generate delivery order" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="154" y="413" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-18" target="w0igsXaTGg2iQcfhWhBG-38" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-18" value="5.Update delivery status" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="424" y="765" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-19" value="6.Receive&lt;br&gt;delivery data" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1118" y="880" width="115" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-20" value="Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="w0igsXaTGg2iQcfhWhBG-6" target="w0igsXaTGg2iQcfhWhBG-7" edge="1">
          <mxGeometry x="0.0051" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-29" target="w0igsXaTGg2iQcfhWhBG-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-28" value="Super APP" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="w0igsXaTGg2iQcfhWhBG-27" vertex="1" connectable="0">
          <mxGeometry x="-0.4842" y="1" relative="1" as="geometry">
            <mxPoint x="12" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-29" value="4.Order Source?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="154" y="534" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="w0igsXaTGg2iQcfhWhBG-31" target="w0igsXaTGg2iQcfhWhBG-49" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-31" value="4a.Send delivery order info to APP" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="442" y="539" width="142" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-35" target="w0igsXaTGg2iQcfhWhBG-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="239" y="795" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-35" value="4b.Fill in delivery confirmation infor" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="164" y="661" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.532;entryY=-0.035;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-29" target="w0igsXaTGg2iQcfhWhBG-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-37" value="Offline" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="w0igsXaTGg2iQcfhWhBG-36" vertex="1" connectable="0">
          <mxGeometry x="-0.6407" y="-1" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-38" target="w0igsXaTGg2iQcfhWhBG-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-38" value="End" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="439" y="880" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-42" target="w0igsXaTGg2iQcfhWhBG-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-42" value="1.Submit for delivery" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="184" y="184" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-49" target="w0igsXaTGg2iQcfhWhBG-35" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="794" y="691" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-52" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="w0igsXaTGg2iQcfhWhBG-51" vertex="1" connectable="0">
          <mxGeometry x="-0.8891" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="w0igsXaTGg2iQcfhWhBG-49" target="w0igsXaTGg2iQcfhWhBG-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="895" y="569" />
              <mxPoint x="895" y="795" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-54" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="w0igsXaTGg2iQcfhWhBG-53" vertex="1" connectable="0">
          <mxGeometry x="-0.6941" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="w0igsXaTGg2iQcfhWhBG-49" value="4a1.Customer APP confirmation?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="714" y="539" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="XYbfhtU7B5Tk8N1oIu71-1" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 153);&quot;&gt;&lt;font style=&quot;color: rgb(255, 0, 0);&quot;&gt;&lt;b style=&quot;&quot;&gt;*&amp;nbsp;&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 15px; text-wrap-mode: wrap;&quot;&gt;&lt;font face=&quot;Inter, -apple-system, BlinkMacSystemFont, Segoe UI, SF Pro SC, SF Pro Display, SF Pro Icons, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif&quot; color=&quot;#ff0000&quot;&gt;&lt;b style=&quot;&quot;&gt;Pending confirmation&lt;/b&gt;&lt;/font&gt;&lt;/span&gt;&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="86" y="709" width="180" height="31" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
