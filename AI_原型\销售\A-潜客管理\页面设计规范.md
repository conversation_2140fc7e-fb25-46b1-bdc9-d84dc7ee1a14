# Perodua DMS 页面设计规范

## 1. 设计系统基础

### 1.1 色彩系统
```css
:root {
    --primary-color: #1890ff;           /* 主色调 - 蓝色 */
    --primary-hover: #40a9ff;           /* 主色调悬停 */
    --success-color: #52c41a;           /* 成功色 - 绿色 */
    --warning-color: #faad14;           /* 警告色 - 黄色 */
    --error-color: #f5222d;             /* 错误色 - 红色 */
    --bg-white: #ffffff;                /* 白色背景 */
    --bg-light: #fafafa;                /* 浅色背景 */
    --border-color: #e8e8e8;            /* 边框色 */
    --text-primary: #333333;            /* 主文本色 */
    --text-secondary: #666666;          /* 次要文本色 */
}
```

### 1.2 字体系统
- **基础字体大小**: 13px
- **小型字体**: 12px（表格、标签）
- **中型字体**: 14px（按钮、分页）
- **大型字体**: 16px（页面标题）
- **字体权重**: 
  - 正常文本: 400
  - 强调文本: 500
  - 标题文本: 600

### 1.3 间距系统
- **超小间距**: 4px
- **小间距**: 8px
- **中间距**: 16px
- **大间距**: 20px
- **超大间距**: 24px

## 2. 布局结构

### 2.1 页面整体布局
```html
<body class="d-flex">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>系统标题</h3>
        </div>
        <div class="sidebar-nav">
            <!-- 导航菜单 -->
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="page-container">
            <!-- 页面内容 -->
        </div>
    </div>
</body>
```

### 2.2 侧边栏设计规则
- **宽度**: 180px
- **背景色**: #1f2937（深灰色）
- **文字色**: #ffffff
- **固定定位**: position: fixed
- **阴影**: 2px 0 8px rgba(0, 0, 0, 0.1)
- **导航项悬停**: 背景色 #374151
- **激活导航项**: 背景色 #1f3a8a，左边框 #3b82f6

### 2.3 主内容区域
- **左边距**: 180px（避开侧边栏）
- **背景色**: #fafafa
- **容器内边距**: 20px
- **最小高度**: 100vh

## 3. 组件设计规则

### 3.1 卡片容器
```css
.card-container {
    background-color: #ffffff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 20px;
}
```

### 3.2 表格设计规则
- **容器**: 白色背景，8px圆角，阴影
- **表头**: 
  - 背景色: #f9fafb
  - 字体大小: 12px
  - 字体权重: 500
  - 内边距: 12px 16px
  - 文本转换: 大写
- **表格行**:
  - 字体大小: 12px
  - 行高: 48px
  - 边框: 1px solid #f3f4f6
  - 悬停背景: #f9fafb
- **固定列**: 
  - 阴影: 2px 0 8px rgba(0, 0, 0, 0.05)
  - 右侧固定: right: 0
  - 左侧固定: left: 0

### 3.3 按钮设计规则
- **基础按钮**:
  - 字体大小: 13px
  - 内边距: 8px 16px
  - 边框圆角: 6px
  - 过渡效果: 0.15s ease
- **小按钮**:
  - 尺寸: 28px × 28px
  - 边框圆角: 4px
  - 字体大小: 12px
- **颜色变体**:
  - 主要按钮: #3b82f6
  - 成功按钮: #10b981
  - 危险按钮: #ef4444
  - 警告按钮: #f59e0b

### 3.4 表单设计规则
- **表单标签**:
  - 字体大小: 13px
  - 颜色: #666666
  - 底边距: 4px
- **输入框**:
  - 字体大小: 13px
  - 边框色: #e8e8e8
  - 边框圆角: 4px
- **表单分组**:
  - 标题字体大小: 13px
  - 标题字体权重: bold
  - 底边框: 1px solid #e8e8e8
  - 上边距: 20px

### 3.5 标签设计规则
```css
.tag {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    letter-spacing: 0.025em;
}

/* 标签颜色变体 */
.tag-success { background-color: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
.tag-warning { background-color: #fef3c7; color: #92400e; border: 1px solid #fde68a; }
.tag-error { background-color: #fee2e2; color: #dc2626; border: 1px solid #fca5a5; }
.tag-primary { background-color: #dbeafe; color: #1d4ed8; border: 1px solid #93c5fd; }
.tag-info { background-color: #f3f4f6; color: #374151; border: 1px solid #d1d5db; }
```

### 3.6 模态框设计规则
- **最大宽度**: 800px（默认）、1000px（大型）
- **标题字体**: 13px，加粗
- **内容区域**: 标准表单布局
- **按钮区域**: 右对齐，间距 8px

## 4. 交互设计规则

### 4.1 悬停效果
- **按钮悬停**: 颜色加深，过渡时间 0.15s
- **表格行悬停**: 背景色 #f9fafb
- **导航项悬停**: 背景色 #374151

### 4.2 激活状态
- **按钮激活**: 背景色根据类型变化
- **导航项激活**: 背景色 #1f3a8a，左边框 #3b82f6
- **选项卡激活**: 背景色 #3b82f6，白色文字

### 4.3 工具提示
- **背景色**: #1f2937
- **文字色**: 白色
- **内边距**: 8px 12px
- **字体大小**: 12px
- **边框圆角**: 6px
- **阴影**: 0 4px 12px rgba(0, 0, 0, 0.25)

## 5. 分页设计规则

### 5.1 分页容器
```css
.pagination-section {
    background-color: #ffffff;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 8px 8px;
}
```

### 5.2 分页按钮
- **基础样式**: 背景 #ffffff，边框 #d1d5db
- **内边距**: 8px 12px
- **字体大小**: 14px
- **边框圆角**: 6px
- **激活状态**: 背景 #3b82f6，白色文字

## 6. 滚动条设计

### 6.1 隐藏滚动条
```css
/* 通用滚动条隐藏 */
.scrollable-element {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.scrollable-element::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}
```

## 7. 响应式设计规则

### 7.1 断点设置
- **小屏幕**: ≤ 1500px
- **表格最小宽度**: 2300px
- **容器最小宽度**: 100%

### 7.2 移动端适配
- **表格**: 启用横向滚动
- **侧边栏**: 考虑折叠或覆盖模式
- **内边距**: 适当减少

## 8. 图标使用规范

### 8.1 图标库
- **使用**: Bootstrap Icons
- **版本**: 1.7.2
- **CDN**: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css

### 8.2 图标尺寸
- **导航图标**: 20px 宽度
- **按钮图标**: 根据按钮大小调整
- **表格图标**: 12px（排序图标）

## 9. 状态指示器

### 9.1 加载状态
- **文本**: "加载中..."
- **位置**: 居中显示
- **颜色**: #666666

### 9.2 空状态
- **图标**: 48px 大小
- **颜色**: #e8e8e8
- **文本**: 居中对齐，#666666

## 10. 多语言支持

### 10.1 语言切换
- **位置**: 页面右上角
- **样式**: 按钮组形式
- **激活状态**: 蓝色背景

### 10.2 文本替换
- **方法**: 使用 t() 函数
- **格式**: `{{ t('key.name') }}`
- **存储**: localStorage 保存语言偏好

## 使用建议

1. **保持一致性**: 所有页面使用相同的设计系统
2. **注重细节**: 遵循间距、颜色、字体规范
3. **响应式友好**: 确保在不同屏幕尺寸下正常显示
4. **性能优化**: 使用CSS变量，避免重复样式
5. **可访问性**: 确保颜色对比度、键盘导航等可访问性要求

这套设计规范确保了整个系统的视觉一致性和用户体验的统一性。在开发新页面时，请严格遵循这些规范。 