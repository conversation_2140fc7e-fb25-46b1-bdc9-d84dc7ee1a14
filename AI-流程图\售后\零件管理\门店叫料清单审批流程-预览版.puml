@startuml 门店叫料清单审批流程
!theme plain
skinparam title {
    RoundCorner 15
    BorderThickness 2
    BorderColor #409EFF
    BackgroundColor #ECF5FF
    FontColor #303133
    FontSize 24
}
skinparam swimlane {
    BorderColor #DCDFE6
    BorderThickness 2
    TitleFontColor #303133
    TitleBackgroundColor #ECF5FF
    TitleFontSize 16
}
skinparam activity {
    BorderColor #409EFF
    BackgroundColor #F5F7FA
    FontColor #606266
    Shadowing false
}
skinparam note {
    BackgroundColor #FEFCE8
    BorderColor #F9E2AF
    FontColor #909399
}
skinparam arrow {
    Color #409EFF
    Thickness 2
}

title 门店叫料清单审批流程

|DMS-店端|
start
:门店操作员新建叫料清单;

label 提交审批
:门店操作员提交审批;

|DMS-厂端|
label 审批环节
:HQ审批员审核叫料清单;

if (审批是否通过?) then (通过)
  :系统自动处理将数据传输给ERP;
  
  |ERP系统|
  :系统自动处理生成采购订单;
  
  |DMS-店端|
  :门店操作员等待收货;
  :门店操作员完成收货确认;
  :系统推送收货数据给ERP;
  
  |库存管理系统|
  :系统自动处理更新库存报表;
  :零件数增加;
  stop
  
else (不通过)
  |DMS-店端|
  
  switch (门店选择?)
  case (修改原单)
    :门店操作员修改叫料清单;
    :系统自动生成新叫料单;
    goto 提交审批
  case (新建叫料单)
    :门店操作员新建叫料清单;
    goto 提交审批
  endswitch
  
endif

@enduml
