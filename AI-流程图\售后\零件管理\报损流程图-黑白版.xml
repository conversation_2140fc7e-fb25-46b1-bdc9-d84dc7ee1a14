<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="Damage Report Flowchart-Black&White" id="damage-report-flow-bw-en">
    <mxGraphModel dx="1577" dy="877" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="swimlane1" value="DMS-Outlet Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="400" height="720" as="geometry" />
        </mxCell>
        <mxCell id="swimlane2" value="DMS-HQ Side" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="440" y="40" width="400" height="720" as="geometry" />
        </mxCell>
        <mxCell id="start1" value="1. Parts Damaged" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="100" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="create_damage" value="2. Create Damage Report" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="200" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="decision1" value="4. Approval Result?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="567" y="391" width="140" height="89" as="geometry" />
        </mxCell>
        <mxCell id="mark_damage" value="5b. Mark Damaged Parts in Inventory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="541" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end1" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="671" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="modify_damage" value="5a. Modify/Redo Damage Report" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="407" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="start1" target="create_damage" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="xdhgRyMJdJyVDp7ayrDl-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="310" y="230" as="sourcePoint" />
            <mxPoint x="540" y="230" as="targetPoint" />
            <Array as="points">
              <mxPoint x="439" y="231" />
              <mxPoint x="637" y="231" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#000000;" parent="1" source="mark_damage" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="240" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-1" value="ERP System" style="swimlane;whiteSpace=wrap;html=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="840" y="40" width="290" height="720" as="geometry" />
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-17" value="6. Receive Data Info" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="xdhgRyMJdJyVDp7ayrDl-1">
          <mxGeometry x="80" y="501" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-6" value="3. Approve Damage Report" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#000000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="567" y="284" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="xdhgRyMJdJyVDp7ayrDl-6" target="decision1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="440" as="sourcePoint" />
            <mxPoint x="710" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-8" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="decision1" target="modify_damage">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="440" as="sourcePoint" />
            <mxPoint x="710" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-10" value="Rejected" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="xdhgRyMJdJyVDp7ayrDl-8">
          <mxGeometry x="-0.2541" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="modify_damage" target="xdhgRyMJdJyVDp7ayrDl-6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="440" as="sourcePoint" />
            <mxPoint x="710" y="390" as="targetPoint" />
            <Array as="points">
              <mxPoint x="240" y="314" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="decision1" target="mark_damage">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="720" y="610" as="sourcePoint" />
            <mxPoint x="770" y="560" as="targetPoint" />
            <Array as="points">
              <mxPoint x="637" y="510" />
              <mxPoint x="240" y="510" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-15" value="Approved" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="xdhgRyMJdJyVDp7ayrDl-14">
          <mxGeometry x="-0.3665" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xdhgRyMJdJyVDp7ayrDl-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="mark_damage">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="740" y="560" as="sourcePoint" />
            <mxPoint x="920" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
