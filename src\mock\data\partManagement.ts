// 零件管理相关的模拟数据

export const partManagementData = {
  // 叫料清单数据
  requisitions: [
    {
      id: 1,
      title: '12月份常规零件补充',
      department: 'service',
      status: 'pending',
      urgency: 'normal',
      createdAt: '2024-12-25',
      parts: [
        { name: '刹车片', quantity: 10, specification: 'BP-001' },
        { name: '机油滤清器', quantity: 20, specification: 'OF-002' }
      ]
    },
    {
      id: 2,
      title: '紧急维修零件申请',
      department: 'service',
      status: 'approved',
      urgency: 'urgent',
      createdAt: '2024-12-24',
      parts: [
        { name: '火花塞', quantity: 8, specification: 'SP-003' }
      ]
    }
  ],

  // 零件库存数据
  inventory: [
    {
      id: 1,
      partNumber: 'BP-001',
      partName: '刹车片',
      currentStock: 25,
      minStock: 10,
      maxStock: 100,
      location: 'A-01-01',
      supplier: '博世汽车'
    },
    {
      id: 2,
      partNumber: 'OF-002',
      partName: '机油滤清器',
      currentStock: 5,
      minStock: 15,
      maxStock: 80,
      location: 'A-01-02',
      supplier: '曼牌滤清器'
    }
  ],

  // 报废记录数据
  scrapRecords: [
    {
      id: 1,
      partName: '刹车片',
      partNumber: 'BP-001',
      quantity: 2,
      reason: 'damaged',
      description: '运输过程中损坏',
      status: 'approved',
      createdAt: '2024-12-25'
    }
  ]
}

export default partManagementData
